import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('admin-session');

    if (!sessionToken) {
      return NextResponse.json({ 
        authenticated: false 
      }, { status: 401 });
    }

    // Simple validation - check if token exists and is not expired
    try {
      const decoded = Buffer.from(sessionToken.value, 'base64').toString();
      const [user, timestamp] = decoded.split(':');
      
      if (user === 'admin') {
        const tokenAge = Date.now() - parseInt(timestamp);
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours
        
        if (tokenAge < maxAge) {
          return NextResponse.json({ 
            authenticated: true 
          });
        }
      }
    } catch (error) {
      console.error('Token validation error:', error);
    }

    return NextResponse.json({ 
      authenticated: false 
    }, { status: 401 });

  } catch (error) {
    console.error('Auth check error:', error);
    return NextResponse.json({ 
      authenticated: false 
    }, { status: 500 });
  }
}
