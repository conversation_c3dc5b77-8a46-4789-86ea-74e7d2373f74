import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Function to check authentication
function checkAuth() {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('admin-session');

  if (!sessionToken) {
    return false;
  }

  try {
    const decoded = Buffer.from(sessionToken.value, 'base64').toString();
    const [user, timestamp] = decoded.split(':');

    if (user === 'admin') {
      const tokenAge = Date.now() - parseInt(timestamp);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      return tokenAge < maxAge;
    }
  } catch (error) {
    return false;
  }

  return false;
}

// Cloudinary upload function
async function uploadToCloudinary(file, metadata) {
  const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
  const apiKey = process.env.CLOUDINARY_API_KEY;
  const apiSecret = process.env.CLOUDINARY_API_SECRET;

  if (!cloudName || !apiKey || !apiSecret) {
    throw new Error('Cloudinary credentials not configured');
  }

  // Convert file to base64
  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);
  const base64Data = `data:${file.type};base64,${buffer.toString('base64')}`;

  // Create unique public_id
  const timestamp = Date.now();
  const publicId = `gallery/${timestamp}_${metadata.title.replace(/[^a-zA-Z0-9]/g, '_')}`;

  // Prepare upload data
  const formData = new FormData();
  formData.append('file', base64Data);
  formData.append('upload_preset', 'gallery_preset'); // You'll create this in Cloudinary
  formData.append('public_id', publicId);
  formData.append('folder', 'express_renos_gallery');
  formData.append('tags', `category:${metadata.category},uploaded:${new Date().toISOString().split('T')[0]}`);

  // Upload to Cloudinary
  const uploadResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!uploadResponse.ok) {
    const errorText = await uploadResponse.text();
    throw new Error(`Cloudinary upload failed: ${uploadResponse.status} - ${errorText}`);
  }

  const uploadResult = await uploadResponse.json();
  
  return {
    publicId: uploadResult.public_id,
    url: uploadResult.secure_url,
    width: uploadResult.width,
    height: uploadResult.height,
    format: uploadResult.format,
    bytes: uploadResult.bytes,
    createdAt: uploadResult.created_at
  };
}

// Store metadata in a simple JSON file (or you could use a database)
async function storeImageMetadata(imageData) {
  // For now, we'll store in a simple array in memory
  // In production, you'd want to use a database like Supabase, PlanetScale, etc.
  
  if (!global.galleryMetadata) {
    global.galleryMetadata = [];
  }

  global.galleryMetadata.push(imageData);
  
  // Also try to store in a more persistent way if possible
  try {
    // You could store this in a database, file system, or another service
    console.log('Image metadata stored:', imageData.filename);
  } catch (error) {
    console.log('Could not persist metadata:', error.message);
  }

  return imageData;
}

export async function POST(request) {
  console.log('=== CLOUDINARY UPLOAD START ===');

  try {
    // Check authentication
    if (!checkAuth()) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file');
    const title = formData.get('title') || 'Untitled';
    const description = formData.get('description') || '';
    const category = formData.get('category') || 'general';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
      }, { status: 400 });
    }

    // Validate file size (10MB max for Cloudinary free tier)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({
        error: 'File too large. Maximum size is 10MB.'
      }, { status: 400 });
    }

    console.log('Uploading to Cloudinary...');
    
    // Upload to Cloudinary
    const cloudinaryResult = await uploadToCloudinary(file, { title, description, category });
    
    // Create metadata object
    const imageMetadata = {
      filename: `${Date.now()}_${file.name}`,
      title,
      description,
      category,
      uploadDate: new Date().toISOString(),
      originalName: file.name,
      size: file.size,
      type: file.type,
      cloudinary: {
        publicId: cloudinaryResult.publicId,
        url: cloudinaryResult.url,
        width: cloudinaryResult.width,
        height: cloudinaryResult.height,
        format: cloudinaryResult.format,
        bytes: cloudinaryResult.bytes
      },
      source: 'cloudinary'
    };

    // Store metadata
    await storeImageMetadata(imageMetadata);

    console.log('✅ Image uploaded to Cloudinary successfully');

    return NextResponse.json({
      success: true,
      message: 'Image uploaded successfully to Cloudinary',
      image: {
        filename: imageMetadata.filename,
        title: imageMetadata.title,
        category: imageMetadata.category,
        url: cloudinaryResult.url,
        uploadDate: imageMetadata.uploadDate
      },
      cloudinary: {
        publicId: cloudinaryResult.publicId,
        url: cloudinaryResult.url,
        optimizedUrl: cloudinaryResult.url.replace('/upload/', '/upload/f_auto,q_auto/'), // Auto-optimized version
      }
    });

  } catch (error) {
    console.error('Cloudinary upload failed:', error);
    return NextResponse.json({
      error: 'Upload failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET endpoint to retrieve images from Cloudinary
export async function GET() {
  try {
    // Return images from global storage (in production, use a database)
    const images = global.galleryMetadata || [];
    
    // Also try to fetch from Cloudinary API if needed
    if (process.env.CLOUDINARY_CLOUD_NAME && process.env.CLOUDINARY_API_KEY && process.env.CLOUDINARY_API_SECRET) {
      // You could fetch images directly from Cloudinary here
      // For now, we'll use the stored metadata
    }

    return NextResponse.json({
      success: true,
      images: images.map(img => ({
        filename: img.filename,
        title: img.title,
        description: img.description,
        category: img.category,
        url: img.cloudinary?.url || '',
        uploadDate: img.uploadDate,
        source: 'cloudinary'
      })),
      totalCount: images.length
    });

  } catch (error) {
    console.error('Failed to retrieve images:', error);
    return NextResponse.json({
      error: 'Failed to retrieve images',
      details: error.message
    }, { status: 500 });
  }
}
