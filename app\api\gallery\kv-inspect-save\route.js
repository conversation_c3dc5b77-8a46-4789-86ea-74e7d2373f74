import { NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import { join } from 'path';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== SAVE KV INSPECTION START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    // Get the detailed inspection data (same logic as kv-inspect-detailed)
    const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (!response.ok) {
      return NextResponse.json({ 
        error: `KV fetch failed: ${response.status}`,
        timestamp: new Date().toISOString()
      });
    }

    const responseText = await response.text();
    let kvData = JSON.parse(responseText);
    let kvImages = kvData.result || [];
    
    // Handle double-encoded JSON
    if (typeof kvImages === 'string') {
      try {
        kvImages = JSON.parse(kvImages);
      } catch (e) {
        kvImages = [];
      }
    }

    const inspectionData = {
      timestamp: new Date().toISOString(),
      rawKvResponse: {
        length: responseText.length,
        preview: responseText.substring(0, 1000),
        full: responseText
      },
      parsedData: {
        type: typeof kvImages,
        isArray: Array.isArray(kvImages),
        length: Array.isArray(kvImages) ? kvImages.length : 'N/A'
      },
      allImages: Array.isArray(kvImages) ? kvImages.map((img, index) => ({
        index,
        filename: img?.filename || 'missing',
        title: img?.title || 'missing',
        description: img?.description || 'missing',
        uploadDate: img?.uploadDate || 'missing',
        hasBase64: !!(img?.base64Data),
        base64Length: img?.base64Data ? img.base64Data.length : 0,
        hasUrl: !!(img?.url),
        hasSize: !!(img?.size),
        hasType: !!(img?.type),
        isValidObject: !!(img && typeof img === 'object' && img.filename && img.base64Data),
        fullObject: img
      })) : [],
      summary: {
        totalImages: Array.isArray(kvImages) ? kvImages.length : 0,
        validImages: Array.isArray(kvImages) ? kvImages.filter(img => img && img.filename && img.base64Data).length : 0,
        invalidImages: Array.isArray(kvImages) ? kvImages.filter(img => !(img && img.filename && img.base64Data)).length : 0
      }
    };

    // Try to save to public directory (if possible in serverless)
    try {
      const filePath = join(process.cwd(), 'public', 'kv-inspection-results.json');
      await writeFile(filePath, JSON.stringify(inspectionData, null, 2));
      
      return NextResponse.json({
        success: true,
        message: 'Inspection results saved to /kv-inspection-results.json',
        fileUrl: '/kv-inspection-results.json',
        summary: inspectionData.summary,
        timestamp: inspectionData.timestamp
      });
    } catch (fileError) {
      // If file save fails, return the data directly (truncated)
      return NextResponse.json({
        success: false,
        message: 'Could not save file in serverless environment',
        fileError: fileError.message,
        summary: inspectionData.summary,
        sampleImages: inspectionData.allImages.slice(0, 3),
        timestamp: inspectionData.timestamp
      });
    }

  } catch (error) {
    console.error('Save KV inspection error:', error);
    return NextResponse.json({
      error: 'Save inspection failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
