import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== DEBUG API TEST START ===');

  try {
    // Import and call the images API directly to avoid HTTP issues
    const { GET: getImages } = await import('../images/route.js');

    console.log('Calling images API directly...');

    const response = await getImages();
    const responseText = await response.text();

    console.log('API Response Status:', response.status);
    console.log('API Response Text Length:', responseText.length);
    console.log('API Response First 500 chars:', responseText.substring(0, 500));

    let parsedData = null;
    try {
      parsedData = JSON.parse(responseText);
    } catch (parseError) {
      console.log('Failed to parse API response as JSON:', parseError.message);
    }
    
    const result = {
      timestamp: new Date().toISOString(),
      test: {
        method: 'direct-call',
        status: response.status,
        ok: response.ok,
        responseLength: responseText.length,
        responsePreview: responseText.substring(0, 500),
        parsedSuccessfully: !!parsedData,
        imageCount: parsedData?.images?.length || 0,
        debugInfo: parsedData?.debug || null,
        sampleImages: parsedData?.images?.slice(0, 3).map(img => ({
          filename: img?.filename,
          title: img?.title,
          url: img?.url,
          source: img?.source
        })) || []
      },
      analysis: {
        apiWorking: response.ok,
        hasImages: (parsedData?.images?.length || 0) > 0,
        imageCount: parsedData?.images?.length || 0,
        likelyIssue: null
      }
    };
    
    // Analysis
    if (!response.ok) {
      result.analysis.likelyIssue = `API returned ${response.status} error`;
    } else if (!parsedData) {
      result.analysis.likelyIssue = 'API response is not valid JSON';
    } else if ((parsedData?.images?.length || 0) === 0) {
      result.analysis.likelyIssue = 'API returns 0 images - this should match your empty KV';
    } else if ((parsedData?.images?.length || 0) === 2) {
      result.analysis.likelyIssue = 'API returns 2 images - matches your frontend count';
    } else if ((parsedData?.images?.length || 0) === 4) {
      result.analysis.likelyIssue = 'API still returns 4 images despite empty KV - source unknown';
    } else {
      result.analysis.likelyIssue = `API returns ${parsedData?.images?.length || 0} images`;
    }
    
    console.log('=== DEBUG API TEST END ===');
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('Debug API test error:', error);
    return NextResponse.json({
      error: 'Debug API test failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
