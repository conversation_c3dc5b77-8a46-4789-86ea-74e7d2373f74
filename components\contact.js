"use client";
import React, { useState, useRef } from "react";
import SendEmail from "./email";

export default function Contact() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const formRef = useRef(null);

  const handleSubmit = async (formData) => {
    setIsSubmitting(true);

    try {
      // Call the server action
      await SendEmail(formData);

      // Show success message
      setShowSuccess(true);

      // Reset the form
      if (formRef.current) {
        formRef.current.reset();
      }

      // Hide success message after 5 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 5000);

    } catch (error) {
      console.error('Error sending email:', error);
      // You could add error handling here if needed
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-1/2 min-w-80 mx-auto py-6 md:bg-transparent bg-white md:rounded-none rounded-lg md:shadow-none shadow-lg md:mt-0 mt-4 md:px-0 px-6">
      <h1 className="w-auto drop-shadow-xl text-center text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-amber-600 via-yellow-200 to-yellow-400 pr-2">
        Request Consultation
        <hr className="border-yellow-300 mt-2 w-3/4 mx-auto"></hr>
      </h1>

      {/* Success Message */}
      {showSuccess && (
        <div className="mb-6 p-4 bg-green-600 text-white rounded-lg text-center animate-fade-in">
          <div className="flex items-center justify-center space-x-2">
            <span className="text-2xl">✅</span>
            <span className="font-medium">Your request has been sent! We will contact you shortly.</span>
          </div>
        </div>
      )}

      <form ref={formRef} action={handleSubmit} className="pt-4 mx-auto">
        <div className="grid md:gap-6">
          <div className="w-full mb-5">
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Name
            </label>
            <input
              type="text"
              name="name"
              id="name"
              className="block py-2.5 px-3 w-full text-sm text-gray-900 bg-white border border-gray-300 rounded-md appearance-none focus:border-yellow-500 focus:outline-none focus:ring-2 focus:ring-yellow-200"
              placeholder="Enter your name"
              required
            />
          </div>
        </div>
        <div className="w-full mb-5">
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Email address
          </label>
          <input
            type="email"
            name="email"
            id="email"
            className="block py-2.5 px-3 w-full text-sm text-gray-900 bg-white border border-gray-300 rounded-md appearance-none focus:border-yellow-500 focus:outline-none focus:ring-2 focus:ring-yellow-200"
            placeholder="Enter your email"
            required
          />
        </div>
        <div className="w-full mb-5">
          <label
            htmlFor="number"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Phone number
          </label>
          <input
            type="tel"
            pattern="[0-9]{3}[0-9]{3}[0-9]{4}"
            name="number"
            id="number"
            className="block py-2.5 px-3 w-full text-sm text-gray-900 bg-white border border-gray-300 rounded-md appearance-none focus:border-yellow-500 focus:outline-none focus:ring-2 focus:ring-yellow-200"
            placeholder="Enter your phone number"
            required
          />
        </div>

        <div className="grid">
          <label
            htmlFor="description"
            className="block mb-2 text-sm font-medium text-gray-700"
          >
            Let us know how we can help!
          </label>
          <textarea
            id="description"
            name="description"
            rows={8}
            className="block p-2.5 w-full text-sm text-gray-900 rounded-lg border border-gray-400 focus:border-yellow-500 bg-white placeholder-gray-500 focus:ring-yellow-500 focus:outline-none"
            placeholder="Describe your project..."
            required
          ></textarea>
          <button
            type="submit"
            disabled={isSubmitting}
            className="mt-4 justify-self-center relative inline-flex items-center justify-center p-0.5 mb-2 me-2 overflow-hidden text-sm font-medium rounded-lg group bg-gradient-to-br from-orange-400 via-yellow-200 to-gray-200 group-hover:from-red-200 group-hover:via-red-300 group-hover:to-yellow-200 text-white hover:text-gray-900 focus:ring-2 focus:outline-none focus:ring-gray-200 focus:ring-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="relative px-5 py-2.5 transition-all ease-in duration-75 bg-black rounded-md group-hover:bg-opacity-0">
              {isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Sending...</span>
                </div>
              ) : (
                'Submit'
              )}
            </span>
          </button>
        </div>
      </form>
      <h1 className="w-auto drop-shadow-xl text-center text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-amber-600 via-yellow-200 to-yellow-400 pr-2">
            Contact us: 
        <hr className="border-yellow-300 mt-2 w-3/4 mx-auto"></hr>
      </h1>
      <div className="pt-5 text-center">
        <h4 className="text-gray-700 font-medium mb-2">Monday - Friday 8am - 5pm</h4>
        <h4 className="text-yellow-600 font-semibold text-lg mb-2">343-550-2133</h4>
        <h4 className="text-gray-600 mb-1"><EMAIL></h4>
        <h4 className="text-gray-600"><EMAIL></h4>
      </div>
      
    </div>
  );
}
