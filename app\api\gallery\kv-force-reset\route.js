import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== FORCE KV RESET API START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check (use your admin password)
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }
    
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    console.log('Starting aggressive KV reset...');

    // Step 1: Try to DELETE the key entirely
    console.log('Step 1: Deleting gallery_images key...');
    const deleteResponse = await fetch(`${process.env.KV_REST_API_URL}/del/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    console.log('Delete response status:', deleteResponse.status);
    if (deleteResponse.ok) {
      console.log('✅ Key deleted successfully');
    } else {
      const deleteError = await deleteResponse.text();
      console.log('Delete failed:', deleteError);
    }

    // Step 2: Wait a moment for propagation
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Step 3: Set fresh empty array
    console.log('Step 2: Setting fresh empty array...');
    const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify([]),
    });

    console.log('Set response status:', setResponse.status);
    if (!setResponse.ok) {
      const setError = await setResponse.text();
      console.error('Set failed:', setError);
      return NextResponse.json({
        error: 'Failed to set empty array',
        details: setError
      }, { status: 500 });
    }

    // Step 4: Reset global storage
    global.galleryImages = [];
    console.log('Reset global storage');

    // Step 5: Wait and verify multiple times
    let verificationAttempts = [];
    
    for (let i = 0; i < 3; i++) {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log(`Verification attempt ${i + 1}...`);
      const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      let attemptResult = { attempt: i + 1 };
      
      if (verifyResponse.ok) {
        const verifyData = await verifyResponse.json();
        let verifyImages = verifyData.result || [];
        
        if (typeof verifyImages === 'string') {
          try {
            verifyImages = JSON.parse(verifyImages);
          } catch (e) {
            attemptResult.parseError = e.message;
            verifyImages = [];
          }
        }
        
        attemptResult.count = Array.isArray(verifyImages) ? verifyImages.length : 'not array';
        attemptResult.type = typeof verifyImages;
        attemptResult.success = Array.isArray(verifyImages) && verifyImages.length === 0;
      } else {
        attemptResult.error = `HTTP ${verifyResponse.status}`;
      }
      
      verificationAttempts.push(attemptResult);
      console.log(`Attempt ${i + 1} result:`, attemptResult);
    }

    const finalSuccess = verificationAttempts.some(attempt => attempt.success);

    const result = {
      success: finalSuccess,
      message: finalSuccess ? 'KV storage force reset successful' : 'KV storage reset may have failed',
      details: {
        deleteAttempted: deleteResponse.ok,
        setAttempted: setResponse.ok,
        verificationAttempts: verificationAttempts,
        finalVerification: verificationAttempts[verificationAttempts.length - 1],
        timestamp: new Date().toISOString()
      }
    };

    console.log('Force reset complete:', result);
    console.log('=== FORCE KV RESET API END ===');

    return NextResponse.json(result);

  } catch (error) {
    console.error('Force KV reset error:', error);
    return NextResponse.json({ 
      error: 'Force KV reset failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
