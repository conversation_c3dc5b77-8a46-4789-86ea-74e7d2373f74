import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== UPLOAD TEST START ===');
  
  try {
    // Check KV configuration
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({
        error: 'KV not configured',
        kvUrl: !!process.env.KV_REST_API_URL,
        kvToken: !!process.env.KV_REST_API_TOKEN
      }, { status: 400 });
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const title = formData.get('title') || 'Test Upload';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    console.log('File received:', file.name, file.size, file.type);

    // Convert to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64Data = buffer.toString('base64');

    // Create test image data
    const imageData = {
      filename: `test_${Date.now()}_${file.name}`,
      title: title,
      description: 'Upload test',
      category: 'test',
      uploadDate: new Date().toISOString(),
      originalName: file.name,
      size: file.size,
      type: file.type,
      base64Data: base64Data,
      mimeType: file.type
    };

    console.log('Image data created:', {
      filename: imageData.filename,
      size: imageData.size,
      base64Length: base64Data.length
    });

    // Try to get existing images from KV
    let existingImages = [];
    try {
      const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (getResponse.ok) {
        const responseText = await getResponse.text();
        console.log('KV get response:', responseText.substring(0, 200));
        
        const kvData = JSON.parse(responseText);
        let images = kvData.result || [];
        
        // Handle double/triple encoding
        if (typeof images === 'string') {
          try {
            images = JSON.parse(images);
          } catch (e) {
            console.log('Failed to parse KV string, using empty array');
            images = [];
          }
        }
        
        if (Array.isArray(images)) {
          existingImages = images;
          console.log('Found existing images:', existingImages.length);
        }
      }
    } catch (getError) {
      console.log('Error getting existing images:', getError.message);
    }

    // Add new image
    existingImages.push(imageData);
    console.log('Total images after adding:', existingImages.length);

    // Store back to KV
    const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(existingImages),
    });

    console.log('KV set response status:', setResponse.status);

    if (setResponse.ok) {
      console.log('✅ Upload test successful');
      return NextResponse.json({
        success: true,
        message: 'Upload test successful',
        filename: imageData.filename,
        totalImages: existingImages.length
      });
    } else {
      const errorText = await setResponse.text();
      console.log('❌ KV set failed:', errorText);
      return NextResponse.json({
        error: 'Failed to store in KV',
        details: errorText
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Upload test error:', error);
    return NextResponse.json({
      error: 'Upload test failed',
      details: error.message
    }, { status: 500 });
  }
}
