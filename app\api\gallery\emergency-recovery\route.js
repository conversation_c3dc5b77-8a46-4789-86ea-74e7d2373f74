import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== EMERGENCY RECOVERY DIAGNOSTIC ===');
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        details: 'Missing KV_REST_API_URL or KV_REST_API_TOKEN'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      kvConfigured: true,
      investigations: []
    };

    // 1. Check the main gallery_images key
    console.log('Checking main gallery_images key...');
    try {
      const mainResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      const mainText = await mainResponse.text();
      results.investigations.push({
        key: 'gallery_images',
        status: mainResponse.status,
        exists: mainResponse.status === 200,
        rawResponse: mainText,
        dataLength: mainText.length,
        parsedData: mainResponse.status === 200 ? (() => {
          try {
            const parsed = JSON.parse(mainText);
            return {
              type: typeof parsed.result,
              isArray: Array.isArray(parsed.result),
              length: Array.isArray(parsed.result) ? parsed.result.length : 'N/A',
              sample: Array.isArray(parsed.result) ? parsed.result.slice(0, 2) : parsed.result
            };
          } catch (e) {
            return { error: 'Parse failed', message: e.message };
          }
        })() : null
      });
    } catch (error) {
      results.investigations.push({
        key: 'gallery_images',
        error: error.message
      });
    }

    // 2. Try to list all keys (if supported by your KV)
    console.log('Attempting to scan for other keys...');
    const possibleKeys = [
      'gallery_images',
      'images',
      'gallery',
      'uploaded_images',
      'gallery_data',
      'image_data',
      'photos',
      'pictures'
    ];

    for (const key of possibleKeys) {
      if (key === 'gallery_images') continue; // Already checked
      
      try {
        const response = await fetch(`${process.env.KV_REST_API_URL}/get/${key}`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        if (response.status === 200) {
          const text = await response.text();
          results.investigations.push({
            key: key,
            status: response.status,
            exists: true,
            dataLength: text.length,
            preview: text.substring(0, 200) + (text.length > 200 ? '...' : '')
          });
        }
      } catch (error) {
        // Ignore errors for non-existent keys
      }
    }

    // 3. Check for backup keys with timestamps
    console.log('Checking for backup keys...');
    const backupKeys = [
      'gallery_images_backup',
      'gallery_images_' + new Date().toISOString().split('T')[0], // Today
      'gallery_images_' + new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
      'gallery_images_' + new Date(Date.now() - 172800000).toISOString().split('T')[0] // Day before
    ];

    for (const key of backupKeys) {
      try {
        const response = await fetch(`${process.env.KV_REST_API_URL}/get/${key}`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        if (response.status === 200) {
          const text = await response.text();
          results.investigations.push({
            key: key,
            status: response.status,
            exists: true,
            dataLength: text.length,
            type: 'backup_key',
            preview: text.substring(0, 200) + (text.length > 200 ? '...' : '')
          });
        }
      } catch (error) {
        // Ignore errors for non-existent keys
      }
    }

    // 4. Summary
    const existingKeys = results.investigations.filter(inv => inv.exists);
    results.summary = {
      totalKeysChecked: results.investigations.length,
      existingKeys: existingKeys.length,
      mainKeyExists: results.investigations.find(inv => inv.key === 'gallery_images')?.exists || false,
      alternativeKeysFound: existingKeys.filter(inv => inv.key !== 'gallery_images').map(inv => inv.key),
      recommendations: []
    };

    // Generate recommendations
    if (!results.summary.mainKeyExists) {
      results.summary.recommendations.push('Main gallery_images key is missing - data may have been deleted');
    }

    if (results.summary.alternativeKeysFound.length > 0) {
      results.summary.recommendations.push(`Found data in alternative keys: ${results.summary.alternativeKeysFound.join(', ')}`);
    }

    if (results.summary.existingKeys === 0) {
      results.summary.recommendations.push('No image data found in KV storage - complete data loss detected');
    }

    console.log('Recovery diagnostic complete');
    return NextResponse.json(results);

  } catch (error) {
    console.error('Emergency recovery diagnostic failed:', error);
    return NextResponse.json({
      error: 'Diagnostic failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
