import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== FINAL KV DIAGNOSIS START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      kvUrl: process.env.KV_REST_API_URL,
      operations: []
    };

    // Step 1: Raw KV GET request
    console.log('Step 1: Raw KV GET request...');
    results.operations.push({ step: 1, operation: 'Raw KV GET', status: 'start' });
    
    const rawResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (rawResponse.ok) {
      const rawText = await rawResponse.text();
      console.log('Raw KV response length:', rawText.length);
      console.log('Raw KV response preview:', rawText.substring(0, 200));
      
      results.rawKvResponse = {
        status: rawResponse.status,
        length: rawText.length,
        fullText: rawText,
        preview: rawText.substring(0, 500)
      };
      
      // Parse the response
      try {
        const kvData = JSON.parse(rawText);
        results.parsedKvData = kvData;
        
        let kvResult = kvData.result;
        results.kvResultType = typeof kvResult;
        results.kvResultLength = typeof kvResult === 'string' ? kvResult.length : 'N/A';
        
        // If it's a string, try to parse it
        if (typeof kvResult === 'string') {
          console.log('KV result is string, attempting to parse...');
          try {
            const parsedResult = JSON.parse(kvResult);
            results.doubleParsedResult = parsedResult;
            results.doubleParsedType = typeof parsedResult;
            results.doubleParsedIsArray = Array.isArray(parsedResult);
            results.doubleParsedLength = Array.isArray(parsedResult) ? parsedResult.length : 'N/A';
            
            if (Array.isArray(parsedResult)) {
              results.imageDetails = parsedResult.map((img, index) => ({
                index,
                filename: img?.filename || 'missing',
                title: img?.title || 'missing',
                uploadDate: img?.uploadDate || 'missing',
                hasBase64: !!(img?.base64Data),
                base64Length: img?.base64Data ? img.base64Data.length : 0,
                fullObject: img
              }));
            }
          } catch (doubleParseError) {
            results.doubleParseError = doubleParseError.message;
            console.log('Failed to double-parse KV string:', doubleParseError.message);
          }
        } else if (Array.isArray(kvResult)) {
          results.directArrayResult = kvResult;
          results.imageDetails = kvResult.map((img, index) => ({
            index,
            filename: img?.filename || 'missing',
            title: img?.title || 'missing',
            uploadDate: img?.uploadDate || 'missing',
            hasBase64: !!(img?.base64Data),
            base64Length: img?.base64Data ? img.base64Data.length : 0,
            fullObject: img
          }));
        }
        
        results.operations.push({ 
          step: 1, 
          operation: 'Raw KV GET', 
          status: 'success',
          responseLength: rawText.length
        });
        
      } catch (parseError) {
        results.parseError = parseError.message;
        results.operations.push({ 
          step: 1, 
          operation: 'Raw KV GET', 
          status: 'parse_failed',
          error: parseError.message
        });
      }
    } else {
      results.operations.push({ 
        step: 1, 
        operation: 'Raw KV GET', 
        status: 'failed',
        httpStatus: rawResponse.status,
        error: await rawResponse.text()
      });
    }

    // Step 2: Try to delete the specific key entirely
    console.log('Step 2: Attempting to delete the entire gallery_images key...');
    results.operations.push({ step: 2, operation: 'Delete KV Key', status: 'start' });
    
    const deleteResponse = await fetch(`${process.env.KV_REST_API_URL}/del/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (deleteResponse.ok) {
      const deleteText = await deleteResponse.text();
      console.log('Delete response:', deleteText);
      results.deleteResponse = deleteText;
      
      results.operations.push({ 
        step: 2, 
        operation: 'Delete KV Key', 
        status: 'success',
        response: deleteText
      });
    } else {
      const deleteError = await deleteResponse.text();
      results.operations.push({ 
        step: 2, 
        operation: 'Delete KV Key', 
        status: 'failed',
        httpStatus: deleteResponse.status,
        error: deleteError
      });
    }

    // Step 3: Wait and verify deletion
    console.log('Step 3: Waiting 5 seconds then verifying deletion...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    results.operations.push({ step: 3, operation: 'Verify Deletion', status: 'start' });
    
    const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (verifyResponse.ok) {
      const verifyText = await verifyResponse.text();
      console.log('Verification response:', verifyText);
      
      results.verificationResponse = {
        status: verifyResponse.status,
        text: verifyText,
        length: verifyText.length
      };
      
      // Check if the key still exists
      try {
        const verifyData = JSON.parse(verifyText);
        const keyExists = verifyData.result !== null;
        
        results.operations.push({ 
          step: 3, 
          operation: 'Verify Deletion', 
          status: 'success',
          keyExists: keyExists,
          result: verifyData.result
        });
        
        if (!keyExists) {
          results.success = true;
          results.message = 'KV key successfully deleted!';
        } else {
          results.message = 'KV key still exists after deletion attempt';
        }
        
      } catch (verifyParseError) {
        results.operations.push({ 
          step: 3, 
          operation: 'Verify Deletion', 
          status: 'parse_failed',
          error: verifyParseError.message
        });
      }
    } else {
      results.operations.push({ 
        step: 3, 
        operation: 'Verify Deletion', 
        status: 'failed',
        httpStatus: verifyResponse.status
      });
    }

    // Step 4: Clear global storage
    console.log('Step 4: Clearing global storage...');
    try {
      global.galleryImages = [];
      results.operations.push({ 
        step: 4, 
        operation: 'Clear Global', 
        status: 'success'
      });
    } catch (globalError) {
      results.operations.push({ 
        step: 4, 
        operation: 'Clear Global', 
        status: 'failed',
        error: globalError.message
      });
    }

    console.log('=== FINAL KV DIAGNOSIS END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('Final KV diagnosis error:', error);
    return NextResponse.json({
      error: 'Final KV diagnosis failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
