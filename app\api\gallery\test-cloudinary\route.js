import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    const results = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      isVercel: !!process.env.VERCEL,
      cloudinaryStatus: {
        cloudName: cloudName ? '✅ Set' : '❌ Missing',
        apiKey: apiKey ? '✅ Set' : '❌ Missing',
        apiSecret: apiSecret ? '✅ Set' : '❌ Missing',
        allConfigured: !!(cloudName && apiKey && apiSecret)
      }
    };

    // If all credentials are present, test the connection
    if (cloudName && apiKey && apiSecret) {
      try {
        // Test Cloudinary connection by making a simple API call
        const testResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/resources/image`, {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`
          }
        });

        if (testResponse.ok) {
          results.connectionTest = '✅ Connection successful';
          results.ready = true;
        } else {
          results.connectionTest = `❌ Connection failed: ${testResponse.status}`;
          results.ready = false;
        }
      } catch (connectionError) {
        results.connectionTest = `❌ Connection error: ${connectionError.message}`;
        results.ready = false;
      }
    } else {
      results.connectionTest = '⏳ Credentials incomplete - cannot test connection';
      results.ready = false;
    }

    // Provide setup instructions if not ready
    if (!results.ready) {
      results.setupInstructions = [
        '1. Create account at cloudinary.com',
        '2. Get your Cloud Name, API Key, and API Secret from the dashboard',
        '3. Add them as environment variables in Vercel:',
        '   - CLOUDINARY_CLOUD_NAME',
        '   - CLOUDINARY_API_KEY', 
        '   - CLOUDINARY_API_SECRET',
        '4. Redeploy your application',
        '5. Test again at this endpoint'
      ];
    } else {
      results.message = '🎉 Cloudinary is ready! Your gallery uploads will now be stored permanently.';
    }

    return NextResponse.json(results);

  } catch (error) {
    return NextResponse.json({
      error: 'Test failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
