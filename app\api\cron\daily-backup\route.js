import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET(request) {
  console.log('=== DAILY BACKUP CRON JOB ===');
  
  try {
    // Verify this is a legitimate cron request (Vercel adds this header)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    const timestamp = new Date().toISOString();
    const today = timestamp.split('T')[0];
    
    // Step 1: Health check
    console.log('Performing health check...');
    const healthResponse = await fetch(`${process.env.VERCEL_URL || 'http://localhost:3000'}/api/gallery/auto-recovery`, {
      method: 'GET'
    });
    
    let healthStatus = 'unknown';
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      healthStatus = healthData.healthy ? 'healthy' : 'unhealthy';
      
      // If unhealthy, attempt auto-recovery
      if (!healthData.healthy) {
        console.log('Data corruption detected, running auto-recovery...');
        const recoveryResponse = await fetch(`${process.env.VERCEL_URL || 'http://localhost:3000'}/api/gallery/auto-recovery`, {
          method: 'POST'
        });
        
        if (recoveryResponse.ok) {
          const recoveryData = await recoveryResponse.json();
          console.log('Auto-recovery completed:', recoveryData.success);
        }
      }
    }

    // Step 2: Create daily backup
    console.log('Creating daily backup...');
    const backupResponse = await fetch(`${process.env.VERCEL_URL || 'http://localhost:3000'}/api/gallery/backup`, {
      method: 'POST'
    });
    
    let backupResult = null;
    if (backupResponse.ok) {
      backupResult = await backupResponse.json();
      console.log('Daily backup created successfully');
    } else {
      console.error('Daily backup failed');
    }

    // Step 3: Cleanup old backups (keep last 30 days)
    console.log('Cleaning up old backups...');
    const cleanupResults = [];
    
    for (let i = 31; i <= 60; i++) { // Delete backups older than 30 days
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - i);
      const oldDateStr = oldDate.toISOString().split('T')[0];
      const oldBackupKey = `gallery_backup_${oldDateStr}`;
      
      try {
        const deleteResponse = await fetch(`${process.env.KV_REST_API_URL}/del/${oldBackupKey}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });
        
        if (deleteResponse.ok) {
          cleanupResults.push(`Deleted old backup: ${oldBackupKey}`);
        }
      } catch (e) {
        // Backup probably doesn't exist, which is fine
      }
    }

    // Step 4: Log the cron job execution
    const cronLogKey = `cron_log_${today}`;
    await fetch(`${process.env.KV_REST_API_URL}/set/${cronLogKey}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        timestamp,
        healthStatus,
        backupCreated: !!backupResult,
        backupImageCount: backupResult?.imageCount || 0,
        cleanupResults,
        executionTime: new Date().toISOString()
      }),
    });

    console.log('Daily backup cron job completed successfully');

    return NextResponse.json({
      success: true,
      message: 'Daily backup cron job completed',
      timestamp,
      healthStatus,
      backup: backupResult,
      cleanup: {
        itemsDeleted: cleanupResults.length,
        details: cleanupResults
      }
    });

  } catch (error) {
    console.error('Daily backup cron job failed:', error);
    
    // Log the error
    try {
      const errorLogKey = `cron_error_${new Date().toISOString().split('T')[0]}`;
      await fetch(`${process.env.KV_REST_API_URL}/set/${errorLogKey}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timestamp: new Date().toISOString(),
          error: error.message,
          stack: error.stack
        }),
      });
    } catch (logError) {
      console.error('Failed to log cron error:', logError);
    }

    return NextResponse.json({
      success: false,
      error: 'Daily backup cron job failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
