import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

function checkAuth() {
  // Simple auth check - in production you'd want proper authentication
  return true; // For now, allow access for debugging
}

export async function POST(request) {
  console.log('=== KV CLEANUP API START ===');
  
  // Check authentication
  if (!checkAuth()) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    // Get current images from KV
    console.log('Fetching current images from KV...');
    const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let currentImages = [];
    if (getResponse.ok) {
      const kvData = await getResponse.json();
      currentImages = kvData.result || [];
      
      // Handle double-encoded JSON
      if (typeof currentImages === 'string') {
        try {
          currentImages = JSON.parse(currentImages);
        } catch (e) {
          console.error('Failed to parse KV data:', e);
          currentImages = [];
        }
      }
    }

    console.log('Current images in KV:', currentImages.length);

    // Validate and clean up images
    const validImages = [];
    const invalidImages = [];
    let totalSize = 0;

    for (const image of currentImages) {
      if (!image || typeof image !== 'object') {
        invalidImages.push({ reason: 'Not an object', data: image });
        continue;
      }

      if (!image.filename || !image.base64Data) {
        invalidImages.push({ 
          reason: 'Missing required fields', 
          filename: image.filename || 'unknown',
          hasBase64: !!image.base64Data 
        });
        continue;
      }

      // Check base64 data validity
      try {
        const buffer = Buffer.from(image.base64Data, 'base64');
        if (buffer.length === 0) {
          invalidImages.push({ 
            reason: 'Empty base64 data', 
            filename: image.filename 
          });
          continue;
        }
        
        totalSize += buffer.length;
        validImages.push(image);
      } catch (e) {
        invalidImages.push({ 
          reason: 'Invalid base64 data', 
          filename: image.filename,
          error: e.message 
        });
      }
    }

    console.log('Valid images:', validImages.length);
    console.log('Invalid images:', invalidImages.length);
    console.log('Total size:', Math.round(totalSize / 1024 / 1024 * 100) / 100, 'MB');

    // Store cleaned images back to KV
    if (validImages.length !== currentImages.length) {
      console.log('Storing cleaned images back to KV...');
      const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validImages),
      });

      if (!setResponse.ok) {
        const errorText = await setResponse.text();
        console.error('Failed to store cleaned images:', errorText);
        return NextResponse.json({
          error: 'Failed to store cleaned images',
          details: errorText
        }, { status: 500 });
      }
    }

    const result = {
      success: true,
      summary: {
        originalCount: currentImages.length,
        validCount: validImages.length,
        invalidCount: invalidImages.length,
        totalSizeMB: Math.round(totalSize / 1024 / 1024 * 100) / 100,
        cleaned: validImages.length !== currentImages.length
      },
      invalidImages: invalidImages,
      timestamp: new Date().toISOString()
    };

    console.log('Cleanup result:', result.summary);
    console.log('=== KV CLEANUP API END ===');

    return NextResponse.json(result);

  } catch (error) {
    console.error('KV cleanup error:', error);
    return NextResponse.json({ 
      error: 'KV cleanup failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
