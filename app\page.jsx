 // @ts-nocheck
"use client";
import React, { useRef } from "react";
import Jumbotron from "../components/jumbotron";
import Services from "../components/services";
import Contact from "../components/contact";
import {} from "react";

export default function Home() {
  const pageData = {
    title: "Express Demos and Renos",
    description:
      `"Turning Houses into Homes"`,
    buttonText: "Request Consultation",
  };
  const title = pageData.title;
  const description = pageData.description;
  const buttonText = pageData.buttonText;

  const scrollDiv = useRef(null);

  const scrollFunc = (div) => {
    return div;
  };

  const refDiv = scrollFunc(scrollDiv);


  return (
    <div className="grid grid-cols-1 mx-auto w-full h-full">
      <div className="">
        <Jumbotron
          className="h-1/4"
          title={title}
          description={description}
          buttonText={buttonText}
          hideButton={false}
          contact={false}
          showLinks={false}
          refDiv={refDiv}
        />
        <Services />
        <Contact />
        <div ref={scrollDiv}></div>
      </div>
    </div>
  );
}
