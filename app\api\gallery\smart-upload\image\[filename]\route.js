import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET(request, { params }) {
  try {
    const { filename } = params;
    
    if (!filename) {
      return new NextResponse('Filename required', { status: 400 });
    }

    // Get image from memory storage
    const images = global.imageStorage || [];
    const image = images.find(img => img.filename === filename);

    if (!image || !image.base64Data) {
      return new NextResponse('Image not found', { status: 404 });
    }

    // Convert base64 back to buffer
    const buffer = Buffer.from(image.base64Data, 'base64');

    // Return the image with proper headers
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': image.type || 'image/jpeg',
        'Content-Length': buffer.length.toString(),
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
      },
    });

  } catch (error) {
    console.error('Failed to serve image:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
