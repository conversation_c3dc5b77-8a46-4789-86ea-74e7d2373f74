"use client";

import Image from "next/image";
import { useState } from "react";

export default function DefaultNavbar(props) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    console.log('Menu toggled:', !isMenuOpen); // Debug log
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className="opacity-75 hover:opacity-100 bg-black z-20 border-y w-full fixed top-0 bg-no-repeat bg-center bg-blend-multiply border-yellow-400 mb-10">
      <div className="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-2">
        {/* Logo */}
        <a href="/" className="flex items-center space-x-3 rtl:space-x-reverse">
          <Image
            src="/logo2.svg"
            className="h-10"
            width={100}
            height={100}
            alt="Express Demos & Renos Logo and Home Button"
          />
        </a>

        {/* Company Name - Hidden on small screens */}
        <a href="/" className="hidden sm:flex items-center space-x-3 rtl:space-x-reverse">
          <span className="self-center lg:text-xl md:text-lg text-md font-semibold whitespace-nowrap text-white">
            EDR - Ottawa/Gatineau
          </span>
        </a>

        {/* Mobile menu button */}
        <button
          onClick={toggleMenu}
          type="button"
          className="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-400 rounded-lg md:hidden hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-gray-600"
          aria-controls="navbar-default"
          aria-expanded={isMenuOpen}
        >
          <span className="sr-only">Open main menu</span>
          <svg
            className="w-5 h-5"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 17 14"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M1 1h15M1 7h15M1 13h15"
            />
          </svg>
        </button>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-4">
          <a
            href="/gallery"
            className="text-white hover:text-yellow-400 transition-colors duration-200 font-medium"
          >
            Gallery
          </a>
          <a
            href="/admin/login"
            className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 text-sm"
            title="Admin Login"
          >
            🔧
          </a>
          <a
            className="z-5"
            data-tooltip-target="tooltip-dark"
            href="https://www.instagram.com/express_demos_renos"
          >
            <Image src="/insta.png" width={25} height={25} alt="Express Demos and Renos Instagram" />
          </a>
        </div>

      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="w-full md:hidden bg-gray-800 border-t border-gray-700">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <a
              href="/gallery"
              className="block px-3 py-2 text-white hover:text-yellow-400 hover:bg-gray-700 rounded-md text-base font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              📸 Gallery
            </a>
            <a
              href="/admin/login"
              className="block px-3 py-2 text-gray-400 hover:text-yellow-400 hover:bg-gray-700 rounded-md text-base"
              onClick={() => setIsMenuOpen(false)}
            >
              🔧 Admin Login
            </a>
            <a
              href="https://www.instagram.com/express_demos_renos"
              className="block px-3 py-2 text-gray-400 hover:text-yellow-400 hover:bg-gray-700 rounded-md text-base"
              onClick={() => setIsMenuOpen(false)}
            >
              📱 Instagram
            </a>
          </div>
        </div>
      )}

      {/* Tooltip for desktop Instagram link */}
      <div
        id="tooltip-dark"
        role="tooltip"
        className="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
      >
        Express Demos & Renos Instagram
        <div className="tooltip-arrow" data-popper-arrow></div>
      </div>
    </nav>
  );
}
