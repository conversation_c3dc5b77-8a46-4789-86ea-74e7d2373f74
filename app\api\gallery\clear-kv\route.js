import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== CLEAR KV STORAGE ===');
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    // Clear the KV storage by setting an empty array
    const response = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify([]), // Empty array
    });

    if (response.ok) {
      console.log('KV storage cleared successfully');
      
      // Also clear global storage
      global.galleryImages = [];
      
      return NextResponse.json({ 
        success: true, 
        message: 'KV storage cleared successfully' 
      });
    } else {
      const errorText = await response.text();
      console.error('Failed to clear KV:', response.status, errorText);
      return NextResponse.json({ 
        error: `Failed to clear KV: ${response.status} ${errorText}` 
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error clearing KV:', error);
    return NextResponse.json({ 
      error: `Error clearing KV: ${error.message}` 
    }, { status: 500 });
  }
}
