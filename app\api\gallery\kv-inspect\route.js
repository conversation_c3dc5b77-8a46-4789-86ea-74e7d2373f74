import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== KV INSPECT ===');
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    // Get raw data from KV
    const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    console.log('KV response status:', response.status);

    if (response.ok) {
      const responseText = await response.text();
      console.log('KV raw response:', responseText);
      
      let kvData;
      try {
        kvData = JSON.parse(responseText);
      } catch (parseError) {
        return NextResponse.json({
          error: 'Failed to parse KV response',
          rawResponse: responseText,
          parseError: parseError.message
        });
      }

      return NextResponse.json({
        success: true,
        kvResponse: {
          status: response.status,
          rawText: responseText,
          parsedData: kvData,
          resultType: typeof kvData.result,
          resultIsArray: Array.isArray(kvData.result),
          resultLength: Array.isArray(kvData.result) ? kvData.result.length : 'N/A',
          resultSample: Array.isArray(kvData.result) ? kvData.result.slice(0, 2) : kvData.result
        },
        globalImages: {
          count: global.galleryImages ? global.galleryImages.length : 0,
          sample: global.galleryImages ? global.galleryImages.slice(0, 2) : []
        }
      });
    } else {
      const errorText = await response.text();
      return NextResponse.json({
        error: 'KV request failed',
        status: response.status,
        statusText: response.statusText,
        errorText: errorText
      });
    }

  } catch (error) {
    console.error('Error inspecting KV:', error);
    return NextResponse.json({ 
      error: `Error inspecting KV: ${error.message}`,
      stack: error.stack
    }, { status: 500 });
  }
}
