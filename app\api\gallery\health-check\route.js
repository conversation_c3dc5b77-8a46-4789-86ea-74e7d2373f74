import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== GALLERY HEALTH CHECK START ===');
  
  try {
    const health = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      warnings: [],
      errors: [],
      metrics: {}
    };

    // Check KV storage health
    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      try {
        const kvResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        if (kvResponse.ok) {
          const kvData = await kvResponse.json();
          let kvImages = kvData.result || [];
          
          // Handle double-encoded JSON
          if (typeof kvImages === 'string') {
            try {
              kvImages = JSON.parse(kvImages);
            } catch (e) {
              health.errors.push('KV data is corrupted string that cannot be parsed');
              health.status = 'unhealthy';
            }
          }

          if (Array.isArray(kvImages)) {
            health.metrics.kvImageCount = kvImages.length;
            
            // Check for corruption indicators
            if (kvImages.length > 1000) {
              health.errors.push(`Possible KV corruption: ${kvImages.length} images (too many)`);
              health.status = 'unhealthy';
            } else if (kvImages.length > 100) {
              health.warnings.push(`High image count: ${kvImages.length} images`);
            }

            // Validate image data quality
            let validImages = 0;
            let invalidImages = 0;
            let totalSize = 0;

            for (const img of kvImages.slice(0, 10)) { // Check first 10 images
              if (img && typeof img === 'object' && img.filename && img.base64Data) {
                validImages++;
                try {
                  const buffer = Buffer.from(img.base64Data, 'base64');
                  totalSize += buffer.length;
                } catch (e) {
                  invalidImages++;
                }
              } else {
                invalidImages++;
              }
            }

            health.metrics.validImagesSample = validImages;
            health.metrics.invalidImagesSample = invalidImages;
            health.metrics.averageImageSizeMB = Math.round(totalSize / validImages / 1024 / 1024 * 100) / 100;

            if (invalidImages > validImages) {
              health.errors.push('Majority of images have invalid data structure');
              health.status = 'unhealthy';
            } else if (invalidImages > 0) {
              health.warnings.push(`${invalidImages} images have invalid data structure`);
            }

          } else {
            health.errors.push(`KV data is not an array: ${typeof kvImages}`);
            health.status = 'unhealthy';
          }

        } else {
          health.errors.push(`KV connection failed: ${kvResponse.status}`);
          health.status = 'unhealthy';
        }

      } catch (kvError) {
        health.errors.push(`KV error: ${kvError.message}`);
        health.status = 'unhealthy';
      }
    } else {
      health.warnings.push('KV not configured');
    }

    // Check global storage
    health.metrics.globalImageCount = global.galleryImages ? global.galleryImages.length : 0;

    // Check for memory issues
    if (global.galleryImages && global.galleryImages.length > 50) {
      health.warnings.push('High memory usage: many images in global storage');
    }

    // Overall health assessment
    if (health.errors.length === 0 && health.warnings.length === 0) {
      health.status = 'healthy';
    } else if (health.errors.length === 0) {
      health.status = 'warning';
    }

    console.log('Health check result:', health.status);
    console.log('=== GALLERY HEALTH CHECK END ===');

    return NextResponse.json(health);

  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      status: 'error',
      errors: [`Health check failed: ${error.message}`],
      warnings: [],
      metrics: {}
    }, { status: 500 });
  }
}
