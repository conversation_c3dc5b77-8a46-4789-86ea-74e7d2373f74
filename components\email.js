"use server"
const SendEmail = async (formData) =>{

    console.log(formData)
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        number: formData.get('number'),
        description: formData.get('description'),
       }


    const nodemailer = require("nodemailer");
    let transporter = nodemailer.createTransport({
      host: "smtp.zohocloud.ca",
      secure: true,
      port: 465,
      auth: {
        user: process.env.ZOHO_USER,
        pass: process.env.ZOHO_PASS,
      },
    });

    const mailOptions = {
      from: "<EMAIL>" 
    }
    if (data.email == undefined || data.email == null) {
      console.log("Insufficient data provided");
    } else {
        console.log(data.description)

      const requestOptions = {
        from: "<EMAIL>",
        to: "<EMAIL>; <EMAIL>",
        contentType: undefined,
        subject:`New Client: ${data.name} - Email: ${data.email} - Phone Number: ${data.number} `,
        html: `<strong>Requested Work:</strong> ${data.description}`,
      }
      const confirmationOptions = {
        from: "<EMAIL>",
        to: data.email,
        contentType: undefined,
        subject: `EDR: Work Request Confirmation`,
        html: `Thank you for choosing <strong>Express Demos & Renos!</strong><br> This is an <strong>automated email</strong> to confirm that we have successfully received your request.<br> We will have one of our professionals reach out to you shortly regarding the next steps, have a great day!`,
      };
      transporter.sendMail(confirmationOptions, (err, info) => {
        if (err) {
          console.log(err);
        } else console.log(info);
      });
      transporter.sendMail(requestOptions, (err, info) => {
        if (err) {
          console.log(err);
        } else console.log(info);
      });
    }
  }

export default SendEmail;