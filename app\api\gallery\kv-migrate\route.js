import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== KV MIGRATE API START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check (use your admin password)
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }
    
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    console.log('Starting KV migration to new key...');

    // Step 1: Create a completely new key with clean data
    const newKeyName = 'gallery_images_v2';
    const cleanData = []; // Start with empty array
    
    console.log('Setting new key:', newKeyName);
    const setNewResponse = await fetch(`${process.env.KV_REST_API_URL}/set/${newKeyName}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(cleanData),
    });

    if (!setNewResponse.ok) {
      const setError = await setNewResponse.text();
      console.error('Failed to set new key:', setError);
      return NextResponse.json({
        error: 'Failed to create new key',
        details: setError
      }, { status: 500 });
    }

    console.log('✅ New key created successfully');

    // Step 2: Verify the new key works
    const verifyNewResponse = await fetch(`${process.env.KV_REST_API_URL}/get/${newKeyName}`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let newKeyVerification = { success: false };
    if (verifyNewResponse.ok) {
      const verifyData = await verifyNewResponse.json();
      const result = verifyData.result || [];
      newKeyVerification = {
        success: true,
        type: typeof result,
        isArray: Array.isArray(result),
        length: Array.isArray(result) ? result.length : 'N/A'
      };
    }

    console.log('New key verification:', newKeyVerification);

    // Step 3: Try to delete the old corrupted key
    console.log('Attempting to delete old corrupted key...');
    const deleteOldResponse = await fetch(`${process.env.KV_REST_API_URL}/del/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    const oldKeyDeleted = deleteOldResponse.ok;
    console.log('Old key deletion:', oldKeyDeleted ? 'success' : 'failed');

    // Step 4: Reset global storage
    global.galleryImages = [];
    console.log('Reset global storage');

    const result = {
      success: newKeyVerification.success,
      message: newKeyVerification.success ? 
        'Migration successful - using new clean KV key' : 
        'Migration failed - new key not working',
      details: {
        newKey: newKeyName,
        newKeyCreated: setNewResponse.ok,
        newKeyVerification: newKeyVerification,
        oldKeyDeleted: oldKeyDeleted,
        nextSteps: newKeyVerification.success ? 
          'Update your code to use the new key name' : 
          'Migration failed, manual intervention needed',
        timestamp: new Date().toISOString()
      }
    };

    console.log('Migration complete:', result);
    console.log('=== KV MIGRATE API END ===');

    return NextResponse.json(result);

  } catch (error) {
    console.error('KV migration error:', error);
    return NextResponse.json({ 
      error: 'KV migration failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
