import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== DEBUG IMAGES API START ===');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Timestamp:', new Date().toISOString());
  
  try {
    // Only check KV storage, skip file system entirely
    let uploadedImages = [];
    let kvError = null;
    let kvDetails = {};
    
    // Check global storage first
    if (global.galleryImages) {
      uploadedImages = global.galleryImages;
      console.log('Global images found:', uploadedImages.length);
    } else {
      console.log('No global images found, initializing...');
      global.galleryImages = [];
    }
    
    // Try Vercel KV
    try {
      if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
        console.log('Attempting KV connection...');
        
        const kvUrl = `${process.env.KV_REST_API_URL}/get/gallery_images`;
        console.log('KV URL preview:', kvUrl.substring(0, 50) + '...');
        
        const response = await fetch(kvUrl, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });
        
        console.log('KV response status:', response.status);
        
        if (response.ok) {
          const responseText = await response.text();
          console.log('KV response length:', responseText.length);
          
          if (responseText.trim()) {
            const kvData = JSON.parse(responseText);
            const kvImages = kvData.result || [];
            console.log('KV images found:', kvImages.length);
            
            if (kvImages.length > 0) {
              uploadedImages = kvImages;
              console.log('Using KV images');
            }
            
            kvDetails = {
              status: 'success',
              imageCount: kvImages.length,
              hasResult: !!kvData.result
            };
          } else {
            kvDetails = { status: 'empty_response' };
          }
        } else {
          const errorText = await response.text();
          kvError = `HTTP ${response.status}: ${errorText}`;
          kvDetails = { status: 'http_error', code: response.status };
        }
      } else {
        kvError = 'KV not configured';
        kvDetails = {
          hasUrl: !!process.env.KV_REST_API_URL,
          hasToken: !!process.env.KV_REST_API_TOKEN
        };
      }
    } catch (error) {
      kvError = error.message;
      kvDetails = { error: error.constructor.name };
      console.error('KV error:', error);
    }
    
    // Ensure uploadedImages is an array
    if (!Array.isArray(uploadedImages)) {
      console.log('uploadedImages is not an array:', typeof uploadedImages, uploadedImages);
      uploadedImages = [];
    }

    console.log('uploadedImages type:', typeof uploadedImages);
    console.log('uploadedImages length:', uploadedImages.length);
    console.log('uploadedImages sample:', uploadedImages.slice(0, 2));

    // Format images for response
    const formattedImages = uploadedImages.map(img => {
      if (!img || typeof img !== 'object') {
        console.log('Invalid image object:', img);
        return null;
      }

      return {
        filename: img.filename || 'unknown',
        url: `/api/gallery/image/${img.filename || 'unknown'}`,
        title: img.title || 'Untitled',
        description: img.description || '',
        category: img.category || 'general',
        uploadDate: img.uploadDate || null,
        originalName: img.originalName || img.filename || 'unknown',
        size: img.size || null,
        type: img.type || 'image/jpeg'
      };
    }).filter(img => img !== null); // Remove any null entries
    
    console.log('Returning', formattedImages.length, 'images');
    console.log('=== DEBUG IMAGES API END ===');
    
    return NextResponse.json({
      images: formattedImages,
      debug: {
        totalCount: formattedImages.length,
        environment: process.env.NODE_ENV,
        kvConfigured: !!(process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN),
        kvError: kvError,
        kvDetails: kvDetails,
        globalImageCount: global.galleryImages ? global.galleryImages.length : 0,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('=== DEBUG API ERROR ===');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    
    return NextResponse.json({
      error: 'Debug API failed',
      details: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
