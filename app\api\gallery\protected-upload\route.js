import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation function to ensure data integrity
function validateImageObject(imageData) {
  const errors = [];
  
  // Required fields
  const requiredFields = ['filename', 'title', 'category', 'base64Data'];
  requiredFields.forEach(field => {
    if (!imageData[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  });
  
  // Validate data types
  if (imageData.filename && typeof imageData.filename !== 'string') {
    errors.push('filename must be a string');
  }
  
  if (imageData.title && typeof imageData.title !== 'string') {
    errors.push('title must be a string');
  }
  
  if (imageData.category && typeof imageData.category !== 'string') {
    errors.push('category must be a string');
  }
  
  if (imageData.base64Data && typeof imageData.base64Data !== 'string') {
    errors.push('base64Data must be a string');
  }
  
  // Validate base64 format
  if (imageData.base64Data) {
    try {
      // Basic base64 validation
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Regex.test(imageData.base64Data)) {
        errors.push('Invalid base64Data format');
      }
    } catch (e) {
      errors.push('base64Data validation failed');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Safe KV operations with backup and rollback
async function safeKVOperation(operation, data) {
  const kvUrl = process.env.KV_REST_API_URL;
  const kvToken = process.env.KV_REST_API_TOKEN;
  
  if (!kvUrl || !kvToken) {
    throw new Error('KV not configured');
  }
  
  // Step 1: Create backup before any operation
  console.log('Creating pre-operation backup...');
  const backupResponse = await fetch(`${kvUrl}/get/gallery_images`, {
    headers: { 'Authorization': `Bearer ${kvToken}` }
  });
  
  let backupData = null;
  if (backupResponse.ok) {
    const backupText = await backupResponse.text();
    backupData = backupText;
    
    // Save backup
    await fetch(`${kvUrl}/set/gallery_backup_pre_upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${kvToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        timestamp: new Date().toISOString(),
        operation: 'pre-upload-backup',
        data: backupText
      }),
    });
  }
  
  // Step 2: Perform the operation
  try {
    const result = await operation();
    
    // Step 3: Verify the operation succeeded
    const verifyResponse = await fetch(`${kvUrl}/get/gallery_images`, {
      headers: { 'Authorization': `Bearer ${kvToken}` }
    });
    
    if (verifyResponse.ok) {
      const verifyText = await verifyResponse.text();
      const verifyData = JSON.parse(verifyText);
      let images = verifyData.result;
      
      // Handle double-encoding
      if (typeof images === 'string') {
        try {
          images = JSON.parse(images);
        } catch (e) {
          throw new Error('Data corruption detected after operation - double encoding');
        }
      }
      
      // Validate the data is still an array
      if (!Array.isArray(images)) {
        throw new Error('Data corruption detected after operation - not an array');
      }
      
      console.log('Operation verified successfully');
      return { success: true, data: images };
    } else {
      throw new Error('Failed to verify operation');
    }
    
  } catch (error) {
    console.error('Operation failed, attempting rollback:', error.message);
    
    // Step 4: Rollback if operation failed
    if (backupData) {
      try {
        const rollbackData = JSON.parse(backupData);
        await fetch(`${kvUrl}/set/gallery_images`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${kvToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(rollbackData.result),
        });
        console.log('Rollback completed successfully');
      } catch (rollbackError) {
        console.error('Rollback failed:', rollbackError.message);
        throw new Error(`Operation failed and rollback failed: ${error.message}`);
      }
    }
    
    throw error;
  }
}

export async function POST(request) {
  console.log('=== PROTECTED UPLOAD SYSTEM ===');
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    // Parse the request
    const formData = await request.formData();
    const file = formData.get('file');
    const title = formData.get('title') || 'Untitled';
    const description = formData.get('description') || '';
    const category = formData.get('category') || 'general';

    if (!file) {
      return NextResponse.json({ 
        error: 'No file provided' 
      }, { status: 400 });
    }

    // Convert file to base64
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64Data = buffer.toString('base64');
    
    // Create image object
    const timestamp = Date.now();
    const filename = `${timestamp}_${file.name}`;
    
    const newImage = {
      filename,
      title,
      description,
      category,
      uploadDate: new Date().toISOString(),
      originalName: file.name,
      size: file.size,
      type: file.type,
      base64Data,
      mimeType: file.type
    };

    // Step 1: Validate the new image data
    const validation = validateImageObject(newImage);
    if (!validation.isValid) {
      return NextResponse.json({
        error: 'Invalid image data',
        details: validation.errors
      }, { status: 400 });
    }

    // Step 2: Perform safe upload operation
    const uploadOperation = async () => {
      // Get current images
      const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      let currentImages = [];
      if (response.ok) {
        const rawText = await response.text();
        const kvData = JSON.parse(rawText);
        let images = kvData.result || [];
        
        // Handle double-encoding
        if (typeof images === 'string') {
          try {
            images = JSON.parse(images);
          } catch (e) {
            console.log('Failed to parse existing data, starting fresh');
            images = [];
          }
        }
        
        if (Array.isArray(images)) {
          currentImages = images;
        }
      }

      // Add new image
      const updatedImages = [...currentImages, newImage];
      
      // Validate the complete array before saving
      if (!Array.isArray(updatedImages)) {
        throw new Error('Updated images is not an array');
      }
      
      // Save to KV with proper encoding (single JSON.stringify)
      const saveResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedImages), // Single encoding only!
      });

      if (!saveResponse.ok) {
        const errorText = await saveResponse.text();
        throw new Error(`Failed to save to KV: ${saveResponse.status} - ${errorText}`);
      }
      
      return updatedImages;
    };

    // Step 3: Execute the safe operation
    const result = await safeKVOperation(uploadOperation, newImage);

    // Step 4: Create post-upload backup
    await fetch(`${process.env.KV_REST_API_URL}/set/gallery_backup_post_upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        timestamp: new Date().toISOString(),
        operation: 'post-upload-backup',
        imageCount: result.data.length,
        lastUploadedImage: {
          filename: newImage.filename,
          title: newImage.title,
          category: newImage.category
        }
      }),
    });

    console.log('Protected upload completed successfully');

    return NextResponse.json({
      success: true,
      message: 'Image uploaded successfully with protection',
      image: {
        filename: newImage.filename,
        title: newImage.title,
        category: newImage.category,
        uploadDate: newImage.uploadDate
      },
      totalImages: result.data.length,
      protections: [
        'Pre-upload backup created',
        'Data validation passed',
        'Post-operation verification completed',
        'Post-upload backup created'
      ]
    });

  } catch (error) {
    console.error('Protected upload failed:', error);
    return NextResponse.json({
      error: 'Upload failed with protection',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
