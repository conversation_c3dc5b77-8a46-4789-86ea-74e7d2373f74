import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== KV RESET API START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check (use your admin password)
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }
    
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    // Get current data to see what we're dealing with
    console.log('Fetching current KV data...');
    const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let currentData = null;
    let currentCount = 0;
    
    if (getResponse.ok) {
      const kvData = await getResponse.json();
      currentData = kvData.result;
      
      if (typeof currentData === 'string') {
        try {
          currentData = JSON.parse(currentData);
        } catch (e) {
          console.log('Current data is corrupted string');
        }
      }
      
      if (Array.isArray(currentData)) {
        currentCount = currentData.length;
      }
    }

    console.log('Current KV data count:', currentCount);

    // Reset KV storage to empty array
    console.log('Resetting KV storage to empty array...');
    const resetResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify([]), // Empty array
    });

    if (!resetResponse.ok) {
      const errorText = await resetResponse.text();
      console.error('Failed to reset KV:', errorText);
      return NextResponse.json({
        error: 'Failed to reset KV storage',
        details: errorText
      }, { status: 500 });
    }

    // Also reset global storage
    global.galleryImages = [];
    console.log('Reset global storage');

    // Verify the reset worked
    const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let verifyCount = 'unknown';
    if (verifyResponse.ok) {
      const verifyData = await verifyResponse.json();
      let verifyImages = verifyData.result || [];
      
      if (typeof verifyImages === 'string') {
        try {
          verifyImages = JSON.parse(verifyImages);
        } catch (e) {
          verifyImages = [];
        }
      }
      
      verifyCount = Array.isArray(verifyImages) ? verifyImages.length : 'not array';
    }

    const result = {
      success: true,
      message: 'KV storage has been reset',
      details: {
        previousCount: currentCount,
        newCount: verifyCount,
        timestamp: new Date().toISOString()
      }
    };

    console.log('Reset complete:', result);
    console.log('=== KV RESET API END ===');

    return NextResponse.json(result);

  } catch (error) {
    console.error('KV reset error:', error);
    return NextResponse.json({ 
      error: 'KV reset failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
