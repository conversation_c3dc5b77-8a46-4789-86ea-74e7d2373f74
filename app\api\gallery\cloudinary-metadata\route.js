import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Function to get all images from Cloudinary
async function getCloudinaryImages() {
  const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
  const apiKey = process.env.CLOUDINARY_API_KEY;
  const apiSecret = process.env.CLOUDINARY_API_SECRET;

  if (!cloudName || !apiKey || !apiSecret) {
    throw new Error('Cloudinary credentials not configured');
  }

  try {
    // Get all images from the gallery folder
    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/resources/image?max_results=500`, {
      headers: {
        'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Cloudinary API error:', response.status, errorText);
      throw new Error(`Cloudinary API failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    
    // Transform Cloudinary resources to our gallery format
    const images = data.resources.map(resource => {
      // Parse metadata from tags and context
      const tags = resource.tags || [];
      const context = resource.context || {};
      
      // Extract category from tags
      const categoryTag = tags.find(tag => tag.startsWith('category:'));
      const category = categoryTag ? categoryTag.replace('category:', '') : 'general';
      
      // Extract upload date from tags
      const uploadedTag = tags.find(tag => tag.startsWith('uploaded:'));
      const uploadDate = uploadedTag ? uploadedTag.replace('uploaded:', '') + 'T00:00:00.000Z' : resource.created_at;
      
      return {
        filename: resource.public_id.split('/').pop(), // Get filename from public_id
        title: context.title || resource.display_name || 'Untitled',
        description: context.description || '',
        category: category,
        uploadDate: uploadDate,
        url: resource.secure_url,
        width: resource.width,
        height: resource.height,
        size: resource.bytes,
        type: `image/${resource.format}`,
        cloudinary: {
          publicId: resource.public_id,
          url: resource.secure_url,
          width: resource.width,
          height: resource.height,
          format: resource.format,
          bytes: resource.bytes
        },
        source: 'cloudinary'
      };
    });

    // Sort by upload date (newest first)
    images.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));

    return images;

  } catch (error) {
    console.error('Failed to fetch from Cloudinary:', error);
    throw error;
  }
}

export async function GET() {
  try {
    const images = await getCloudinaryImages();
    
    return NextResponse.json({
      success: true,
      images,
      totalCount: images.length,
      source: 'cloudinary',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Failed to retrieve images from Cloudinary:', error);
    return NextResponse.json({
      error: 'Failed to retrieve images',
      details: error.message,
      source: 'cloudinary'
    }, { status: 500 });
  }
}
