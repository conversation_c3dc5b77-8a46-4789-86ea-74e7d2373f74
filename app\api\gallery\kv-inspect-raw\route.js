import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== KV RAW INSPECT START ===');
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    const inspection = {
      timestamp: new Date().toISOString(),
      kvUrl: process.env.KV_REST_API_URL.substring(0, 50) + '...',
      hasToken: !!process.env.KV_REST_API_TOKEN
    };

    // Get raw response
    console.log('Fetching raw KV data...');
    const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    inspection.httpStatus = response.status;
    inspection.httpStatusText = response.statusText;
    inspection.headers = Object.fromEntries(response.headers.entries());

    if (response.ok) {
      const rawText = await response.text();
      inspection.rawResponseLength = rawText.length;
      inspection.rawResponsePreview = rawText.substring(0, 500);
      
      try {
        const parsed = JSON.parse(rawText);
        inspection.parsedStructure = {
          hasResult: 'result' in parsed,
          resultType: typeof parsed.result,
          resultLength: parsed.result ? parsed.result.length : 'N/A'
        };

        if (parsed.result) {
          if (typeof parsed.result === 'string') {
            inspection.resultIsString = true;
            inspection.resultStringLength = parsed.result.length;
            inspection.resultStringPreview = parsed.result.substring(0, 200);
            
            // Try to parse the string
            try {
              const doubleParsed = JSON.parse(parsed.result);
              inspection.doubleParseSuccess = true;
              inspection.doubleParsedType = typeof doubleParsed;
              inspection.doubleParsedLength = Array.isArray(doubleParsed) ? doubleParsed.length : 'not array';
              
              if (Array.isArray(doubleParsed)) {
                inspection.sampleItems = doubleParsed.slice(0, 3).map(item => ({
                  type: typeof item,
                  isObject: typeof item === 'object' && item !== null,
                  hasFilename: item && typeof item === 'object' && 'filename' in item,
                  preview: typeof item === 'string' ? item.substring(0, 50) : JSON.stringify(item).substring(0, 50)
                }));
              }
            } catch (doubleParseError) {
              inspection.doubleParseError = doubleParseError.message;
            }
          } else if (Array.isArray(parsed.result)) {
            inspection.resultIsArray = true;
            inspection.arrayLength = parsed.result.length;
            inspection.sampleItems = parsed.result.slice(0, 3).map(item => ({
              type: typeof item,
              isObject: typeof item === 'object' && item !== null,
              hasFilename: item && typeof item === 'object' && 'filename' in item,
              preview: typeof item === 'string' ? item.substring(0, 50) : JSON.stringify(item).substring(0, 50)
            }));
          }
        }
      } catch (parseError) {
        inspection.parseError = parseError.message;
      }
    } else {
      const errorText = await response.text();
      inspection.errorResponse = errorText;
    }

    console.log('Raw inspection complete');
    console.log('=== KV RAW INSPECT END ===');

    return NextResponse.json(inspection);

  } catch (error) {
    console.error('Raw inspect error:', error);
    return NextResponse.json({
      error: 'Raw inspect failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
