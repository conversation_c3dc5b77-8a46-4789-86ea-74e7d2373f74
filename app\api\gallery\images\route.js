import { NextResponse } from 'next/server';
import { readFile, readdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== IMAGES API START ===');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Timestamp:', new Date().toISOString());

  try {
    // Initialize with empty array
    let uploadedImages = [];

    // Initialize global storage
    if (!global.galleryImages) {
      global.galleryImages = [];
    }

    console.log('Global images found:', global.galleryImages.length);

    // ALWAYS check KV storage for persistent images (don't skip based on global)
    let kvSuccess = false;
    let kvImages = [];

    console.log('Checking KV storage...');
    try {
        if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
        console.log('Attempting to read from Vercel KV...');

        const kvUrl = `${process.env.KV_REST_API_URL}/get/gallery_images`;
        console.log('KV URL (first 50 chars):', kvUrl.substring(0, 50) + '...');

        const response = await fetch(kvUrl, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        console.log('KV response status:', response.status);
        console.log('KV response ok:', response.ok);

        if (response.ok) {
          const responseText = await response.text();
          console.log('KV raw response length:', responseText.length);
          console.log('KV raw response preview:', responseText.substring(0, 200));

          if (responseText.trim()) {
            const kvData = JSON.parse(responseText);
            console.log('KV data parsed successfully');
            console.log('KV data keys:', Object.keys(kvData));

            kvImages = kvData.result || [];

            // Handle double-encoded JSON from KV
            if (typeof kvImages === 'string') {
              console.log('KV result is string, parsing again. Length:', kvImages.length);
              try {
                kvImages = JSON.parse(kvImages);
                console.log('Successfully parsed KV string to array:', Array.isArray(kvImages) ? kvImages.length : 'not an array');
              } catch (parseError) {
                console.log('Failed to parse KV string:', parseError.message);
                kvImages = [];
              }
            }

            console.log('KV images type:', typeof kvImages);
            console.log('KV images is array:', Array.isArray(kvImages));
            console.log('KV images length:', Array.isArray(kvImages) ? kvImages.length : 'N/A');

            // Validate KV data - if it's corrupted (too many images), reset it
            if (Array.isArray(kvImages)) {
              if (kvImages.length > 1000) {
                console.log('⚠️ KV data appears corrupted (too many images):', kvImages.length);
                console.log('Sample data:', kvImages.slice(0, 2));

                // Check if the data looks like valid image objects
                const validImages = kvImages.filter(img =>
                  img &&
                  typeof img === 'object' &&
                  img.filename &&
                  img.base64Data &&
                  typeof img.filename === 'string' &&
                  typeof img.base64Data === 'string'
                );

                console.log('Valid images after filtering:', validImages.length);

                if (validImages.length < kvImages.length * 0.1) {
                  console.log('🚨 KV data is severely corrupted, using empty array');
                  kvImages = [];
                } else {
                  console.log('✅ Using filtered valid images');
                  kvImages = validImages;
                }
              }

              if (kvImages.length > 0) {
                kvSuccess = true;
                console.log('Found KV images:', kvImages.length);
              }
            }
          } else {
            console.log('KV returned empty response');
          }
        } else {
          const errorText = await response.text();
          console.log('KV error response:', response.status, response.statusText);
          console.log('KV error body:', errorText);
        }
      } else {
        console.log('Vercel KV not configured');
        console.log('KV URL exists:', !!process.env.KV_REST_API_URL);
        console.log('KV Token exists:', !!process.env.KV_REST_API_TOKEN);
      }
      } catch (kvError) {
        console.error('Vercel KV read error:', kvError.message);
        console.error('KV Error type:', kvError.constructor.name);
        // Don't log full stack in production to avoid noise
        if (process.env.NODE_ENV !== 'production') {
          console.error('KV Error stack:', kvError.stack);
        }
      }

    // Combine global and KV images (KV takes priority for persistence)
    uploadedImages = [...kvImages, ...global.galleryImages];
    console.log('Combined images - KV:', kvImages.length, 'Global:', global.galleryImages.length, 'Total:', uploadedImages.length);

    console.log('Final uploaded images count:', uploadedImages.length);
    console.log('KV success:', kvSuccess);

    // Also try to get images from the file system (for existing images and uploads)
    let fileSystemImages = [];
    try {
      // Check both images and uploads directories
      const directories = [
        { dir: path.join(process.cwd(), 'public/images'), urlPrefix: '/images/' },
        { dir: path.join(process.cwd(), 'public/uploads'), urlPrefix: '/uploads/' }
      ];

      for (const { dir, urlPrefix } of directories) {
        console.log('Checking directory:', dir);

        if (existsSync(dir)) {
          // Get all image files
          const files = await readdir(dir);
          const imageFiles = files.filter(file =>
            /\.(jpg|jpeg|png|gif|webp)$/i.test(file) && file !== 'metadata.json'
          );

          console.log(`Found ${imageFiles.length} images in ${dir}`);

          // DISABLED: Load metadata if it exists
          // This was causing persistent images to appear from old metadata.json files
          const metadataPath = path.join(dir, 'metadata.json');
          let metadata = {};

          // COMMENTED OUT TO FIX PERSISTENT IMAGES ISSUE
          // if (existsSync(metadataPath)) {
          //   try {
          //     const metadataContent = await readFile(metadataPath, 'utf8');
          //     metadata = JSON.parse(metadataContent);
          //     console.log('⚠️ METADATA LOADING DISABLED - Found metadata file but ignoring it');
          //   } catch (error) {
          //     console.error('Error reading metadata:', error);
          //   }
          // }

          console.log('✅ Metadata loading disabled - no persistent images from metadata.json');

          // Add images from this directory
          const dirImages = imageFiles.map(filename => ({
            filename,
            url: `${urlPrefix}${filename}`,
            title: metadata[filename]?.title || filename.replace(/\.[^/.]+$/, ""),
            description: metadata[filename]?.description || '',
            category: metadata[filename]?.category || 'general',
            uploadDate: metadata[filename]?.uploadDate || null,
            originalName: metadata[filename]?.originalName || filename,
            size: metadata[filename]?.size || null,
            type: metadata[filename]?.type || null,
            source: 'filesystem'
          }));

          fileSystemImages.push(...dirImages);
        }
      }
    } catch (error) {
      console.log('Could not read file system images (read-only environment):', error.message);
      // Skip file system images in serverless - this is expected
      fileSystemImages = [];
    }

    // Ensure uploadedImages is an array
    if (!Array.isArray(uploadedImages)) {
      console.log('uploadedImages is not an array:', typeof uploadedImages, uploadedImages);
      uploadedImages = [];
    }

    // Ensure fileSystemImages is an array
    if (!Array.isArray(fileSystemImages)) {
      console.log('fileSystemImages is not an array:', typeof fileSystemImages);
      fileSystemImages = [];
    }

    // Combine uploaded images and file system images
    const allImages = [
      ...uploadedImages.map(img => {
        if (!img || typeof img !== 'object') {
          console.log('Invalid uploaded image object:', img);
          return null;
        }
        return {
          filename: img.filename || 'unknown',
          url: img.url || `/api/gallery/image/${img.filename || 'unknown'}`, // Use direct URL if available, fallback to API
          title: img.title || 'Untitled',
          description: img.description || '',
          category: img.category || 'general',
          uploadDate: img.uploadDate || null,
          originalName: img.originalName || img.filename || 'unknown',
          size: img.size || null,
          type: img.type || 'image/jpeg'
        };
      }).filter(img => img !== null),
      ...fileSystemImages
    ];

    // Sort by upload date (newest first)
    allImages.sort((a, b) => {
      if (!a.uploadDate) return 1;
      if (!b.uploadDate) return -1;
      return new Date(b.uploadDate) - new Date(a.uploadDate);
    });

    console.log('Final image count breakdown:');
    console.log('- Uploaded images:', uploadedImages.length);
    console.log('- File system images:', fileSystemImages.length);
    console.log('- Total images:', allImages.length);
    console.log('- Sample image URLs:', allImages.slice(0, 3).map(img => img.url));

    return NextResponse.json({
      images: allImages,
      debug: {
        uploadedCount: uploadedImages.length,
        fileSystemCount: fileSystemImages.length,
        totalCount: allImages.length,
        environment: process.env.NODE_ENV,
        kvConfigured: !!(process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN)
      }
    });

  } catch (error) {
    console.error('=== IMAGES API ERROR ===');
    console.error('Error fetching images:', error);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    console.log('=== IMAGES API END (ERROR) ===');

    return NextResponse.json({
      error: 'Failed to fetch images',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
