import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== KV KEY INSPECTOR START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      kvUrl: process.env.KV_REST_API_URL,
      operations: [],
      keys: {}
    };

    // List of possible keys to check
    const keysToCheck = [
      'gallery_images',
      'galleryImages', 
      'images',
      'gallery',
      'uploads',
      'test_images',
      'persistent_images'
    ];

    console.log('Checking KV keys:', keysToCheck);

    // Check each possible key
    for (const key of keysToCheck) {
      console.log(`Checking key: ${key}`);
      
      try {
        const response = await fetch(`${process.env.KV_REST_API_URL}/get/${key}`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        if (response.ok) {
          const responseText = await response.text();
          const kvData = JSON.parse(responseText);
          let result = kvData.result;
          
          results.keys[key] = {
            exists: true,
            rawType: typeof result,
            rawLength: typeof result === 'string' ? result.length : 'N/A',
            rawPreview: typeof result === 'string' ? result.substring(0, 100) : JSON.stringify(result).substring(0, 100)
          };
          
          // Try to parse if it's a string
          if (typeof result === 'string') {
            try {
              const parsed = JSON.parse(result);
              results.keys[key].parsedType = typeof parsed;
              results.keys[key].parsedIsArray = Array.isArray(parsed);
              results.keys[key].parsedLength = Array.isArray(parsed) ? parsed.length : 'N/A';
              
              if (Array.isArray(parsed) && parsed.length > 0) {
                results.keys[key].sampleItems = parsed.slice(0, 2).map(item => ({
                  filename: item?.filename || 'unknown',
                  title: item?.title || 'unknown',
                  uploadDate: item?.uploadDate || 'unknown'
                }));
              }
            } catch (parseError) {
              results.keys[key].parseError = parseError.message;
            }
          } else if (Array.isArray(result)) {
            results.keys[key].parsedType = 'array';
            results.keys[key].parsedIsArray = true;
            results.keys[key].parsedLength = result.length;
            
            if (result.length > 0) {
              results.keys[key].sampleItems = result.slice(0, 2).map(item => ({
                filename: item?.filename || 'unknown',
                title: item?.title || 'unknown',
                uploadDate: item?.uploadDate || 'unknown'
              }));
            }
          }
          
          console.log(`Key ${key}: exists, type=${typeof result}, length=${results.keys[key].rawLength}`);
        } else {
          results.keys[key] = {
            exists: false,
            httpStatus: response.status,
            error: await response.text()
          };
          console.log(`Key ${key}: does not exist (${response.status})`);
        }
      } catch (error) {
        results.keys[key] = {
          exists: false,
          error: error.message
        };
        console.log(`Key ${key}: error - ${error.message}`);
      }
    }

    // Try to list all keys (if supported)
    console.log('Attempting to list all keys...');
    try {
      const listResponse = await fetch(`${process.env.KV_REST_API_URL}/keys/*`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (listResponse.ok) {
        const listText = await listResponse.text();
        const listData = JSON.parse(listText);
        results.allKeys = listData.result || [];
        console.log('All keys found:', results.allKeys);
      } else {
        results.allKeysError = `Failed to list keys: ${listResponse.status}`;
        console.log('Failed to list all keys:', listResponse.status);
      }
    } catch (listError) {
      results.allKeysError = listError.message;
      console.log('Error listing keys:', listError.message);
    }

    // Summary
    const existingKeys = Object.keys(results.keys).filter(key => results.keys[key].exists);
    const keysWithImages = existingKeys.filter(key => {
      const keyData = results.keys[key];
      return keyData.parsedLength > 0;
    });

    results.summary = {
      totalKeysChecked: keysToCheck.length,
      existingKeys: existingKeys.length,
      keysWithImages: keysWithImages.length,
      existingKeyNames: existingKeys,
      keysWithImageNames: keysWithImages
    };

    console.log('=== KV KEY INSPECTOR END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('KV key inspector error:', error);
    return NextResponse.json({
      error: 'KV key inspection failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
