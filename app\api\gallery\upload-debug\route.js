import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== UPLOAD DEBUG TEST START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      kvConfig: {
        hasUrl: !!process.env.KV_REST_API_URL,
        hasToken: !!process.env.KV_REST_API_TOKEN,
        urlPreview: process.env.KV_REST_API_URL ? process.env.KV_REST_API_URL.substring(0, 50) + '...' : 'none'
      },
      steps: [],
      currentKvData: null,
      error: null
    };

    // Step 1: Check KV configuration
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      results.error = 'KV not configured';
      results.steps.push({
        step: 1,
        operation: 'Check KV Config',
        status: 'error',
        message: 'KV environment variables missing'
      });
      return NextResponse.json(results);
    }

    results.steps.push({
      step: 1,
      operation: 'Check KV Config',
      status: 'success',
      message: 'KV environment variables found'
    });

    // Step 2: Try to read current KV data
    try {
      console.log('Reading current KV data...');
      
      const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      results.steps.push({
        step: 2,
        operation: 'Read KV Data',
        status: getResponse.ok ? 'success' : 'error',
        httpStatus: getResponse.status,
        message: getResponse.ok ? 'KV read successful' : `KV read failed: ${getResponse.status}`
      });

      if (getResponse.ok) {
        const responseText = await getResponse.text();
        const kvData = JSON.parse(responseText);
        let existingImages = kvData.result || [];
        
        // Handle double-encoded JSON
        if (typeof existingImages === 'string') {
          try {
            existingImages = JSON.parse(existingImages);
          } catch (e) {
            existingImages = [];
          }
        }
        
        results.currentKvData = {
          rawLength: responseText.length,
          imageCount: Array.isArray(existingImages) ? existingImages.length : 'not array',
          sampleImages: Array.isArray(existingImages) ? existingImages.slice(0, 2).map(img => ({
            filename: img?.filename,
            title: img?.title,
            uploadDate: img?.uploadDate
          })) : []
        };

        // Step 3: Try to write test data to KV
        console.log('Testing KV write...');
        
        const testImage = {
          filename: 'test_upload_debug.png',
          title: 'Upload Debug Test',
          description: 'Test image for debugging upload issues',
          uploadDate: new Date().toISOString(),
          base64Data: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          size: 100,
          type: 'image/png'
        };

        const testArray = [...existingImages, testImage];
        
        const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testArray),
        });

        const setResponseText = await setResponse.text();

        results.steps.push({
          step: 3,
          operation: 'Test KV Write',
          status: setResponse.ok ? 'success' : 'error',
          httpStatus: setResponse.status,
          message: setResponse.ok ? `KV write successful, saved ${testArray.length} images` : `KV write failed: ${setResponse.status}`,
          requestSize: JSON.stringify(testArray).length,
          responseBody: setResponseText
        });

        // Step 4: Verify the write by reading back
        if (setResponse.ok) {
          console.log('Verifying KV write...');
          
          const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
            headers: {
              'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            },
          });

          if (verifyResponse.ok) {
            const verifyText = await verifyResponse.text();
            const verifyData = JSON.parse(verifyText);
            let verifyImages = verifyData.result || [];
            
            if (typeof verifyImages === 'string') {
              try {
                verifyImages = JSON.parse(verifyImages);
              } catch (e) {
                verifyImages = [];
              }
            }

            results.steps.push({
              step: 4,
              operation: 'Verify KV Write',
              status: 'success',
              message: `Verification successful, KV now has ${Array.isArray(verifyImages) ? verifyImages.length : 'unknown'} images`,
              verifiedCount: Array.isArray(verifyImages) ? verifyImages.length : 'unknown',
              hasTestImage: Array.isArray(verifyImages) ? verifyImages.some(img => img?.filename === 'test_upload_debug.png') : false
            });

            // Clean up - remove test image
            const cleanArray = existingImages; // Original without test image
            await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(cleanArray),
            });

            results.steps.push({
              step: 5,
              operation: 'Cleanup Test Image',
              status: 'success',
              message: 'Test image removed, KV restored to original state'
            });

          } else {
            results.steps.push({
              step: 4,
              operation: 'Verify KV Write',
              status: 'error',
              message: `Verification failed: ${verifyResponse.status}`
            });
          }
        }

      } else {
        const errorText = await getResponse.text();
        results.error = `KV read failed: ${getResponse.status} - ${errorText}`;
      }

    } catch (kvError) {
      results.error = kvError.message;
      results.steps.push({
        step: 2,
        operation: 'Read KV Data',
        status: 'error',
        message: kvError.message
      });
    }

    console.log('=== UPLOAD DEBUG TEST END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('Upload debug error:', error);
    return NextResponse.json({
      error: 'Upload debug failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
