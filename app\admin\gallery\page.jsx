'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

export default function GalleryAdmin() {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [authenticated, setAuthenticated] = useState(false);
  const [checkingAuth, setCheckingAuth] = useState(true);
  const [uploadForm, setUploadForm] = useState({
    title: '',
    description: '',
    category: 'general'
  });
  const router = useRouter();

  const categories = [
    'general',
    'demolition',
    'renovation',
    'flooring',
    'painting',
    'tiling',
    'landscaping',
    'before-after'
  ];

  // Function to compress image for Vercel serverless limits
  const compressImage = (file, maxWidth = 1200, quality = 0.8) => {
    return new Promise((resolve, reject) => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Use window.Image to avoid conflict with Next.js Image component
        const img = new window.Image();

        img.onload = function() {
          try {
            // Calculate new dimensions
            let { width, height } = img;

            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }

            // Set canvas size
            canvas.width = width;
            canvas.height = height;

            // Draw and compress
            ctx.drawImage(img, 0, 0, width, height);

            // Convert to blob with compression
            canvas.toBlob(function(blob) {
              if (blob) {
                resolve(blob);
              } else {
                console.warn('Canvas toBlob failed, using original file');
                resolve(file);
              }
            }, 'image/jpeg', quality);
          } catch (error) {
            console.error('Error during image processing:', error);
            resolve(file);
          }
        };

        img.onerror = function() {
          console.error('Error loading image for compression');
          resolve(file); // Return original file if compression fails
        };

        // Create object URL for the image
        const objectUrl = URL.createObjectURL(file);
        img.src = objectUrl;

        // Clean up object URL after a delay to prevent memory leaks
        setTimeout(() => {
          URL.revokeObjectURL(objectUrl);
        }, 1000);

      } catch (error) {
        console.error('Error in compressImage function:', error);
        resolve(file);
      }
    });
  };

  const checkAuthentication = useCallback(async () => {
    try {
      const response = await fetch('/api/auth/check');
      if (response.ok) {
        setAuthenticated(true);
      } else {
        router.push('/admin/login');
      }
    } catch (error) {
      console.error('Auth check error:', error);
      router.push('/admin/login');
    } finally {
      setCheckingAuth(false);
    }
  }, [router]);

  useEffect(() => {
    checkAuthentication();
  }, [checkAuthentication]);

  useEffect(() => {
    if (authenticated) {
      fetchImages();
    }
  }, [authenticated]);

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      router.push('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const fetchImages = async () => {
    try {
      console.log('🖼️ Fetching images from /api/gallery/simple-cloudinary...');
      const response = await fetch('/api/gallery/simple-cloudinary');
      console.log('📡 Fetch response status:', response.status);

      const responseText = await response.text();
      console.log('📄 Raw response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log('✅ Parsed JSON data:', data);
      } catch (parseError) {
        console.error('❌ Failed to parse JSON:', parseError);
        console.error('Response was:', responseText);
        return;
      }

      console.log('📊 Images in response:', data.images?.length || 0);
      console.log('📋 Image details:', data.images);

      setImages(data.images || []);
      console.log('🎯 Images set to state, total:', data.images?.length || 0);
    } catch (error) {
      console.error('❌ Error fetching images:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (e) => {
    console.log('🚀 UPLOAD FUNCTION CALLED!');
    e.preventDefault();
    const fileInput = e.target.file;
    const file = fileInput.files[0];

    console.log('=== UPLOAD DEBUG START ===');
    console.log('File input:', fileInput);
    console.log('Selected file:', file);
    console.log('File details:', file ? {
      name: file.name,
      size: file.size,
      type: file.type
    } : 'No file');

    if (!file) {
      alert('Please select a file');
      return;
    }

    setUploading(true);

    try {
      // Compress image if it's too large
      let fileToUpload = file;

      console.log('Original file size:', (file.size / 1024 / 1024).toFixed(2), 'MB');

      if (file.size > 1.5 * 1024 * 1024) { // If larger than 1.5MB, compress
        console.log('File is large, compressing...');
        fileToUpload = await compressImage(file, 1200, 0.7);
        console.log('Compressed file size:', (fileToUpload.size / 1024 / 1024).toFixed(2), 'MB');

        // Create a new File object with the original name
        fileToUpload = new File([fileToUpload], file.name, {
          type: 'image/jpeg',
          lastModified: Date.now(),
        });
      }

      const formData = new FormData();
      formData.append('file', fileToUpload);
      formData.append('title', uploadForm.title);
      formData.append('description', uploadForm.description);
      formData.append('category', uploadForm.category);

      console.log('FormData created with:');
      console.log('- file:', fileToUpload.name);
      console.log('- title:', uploadForm.title);
      console.log('- description:', uploadForm.description);
      console.log('- category:', uploadForm.category);

      console.log('Sending upload request to /api/gallery/simple-cloudinary...');
      const response = await fetch('/api/gallery/simple-cloudinary', {
        method: 'POST',
        body: formData,
      });

      console.log('Response received:');
      console.log('- Status:', response.status);
      console.log('- Status Text:', response.statusText);

      const responseText = await response.text();
      console.log('Raw response text:', responseText);

      let result;
      try {
        result = JSON.parse(responseText);
        console.log('Parsed JSON result:', result);
      } catch (parseError) {
        console.error('Failed to parse JSON:', parseError);
        console.error('Response was not valid JSON:', responseText);
        alert(`Upload failed: Server returned invalid response: ${responseText.substring(0, 100)}...`);
        return;
      }

      if (result.success) {
        alert('Image uploaded successfully!');
        setUploadForm({ title: '', description: '', category: 'general' });
        fileInput.value = '';
        console.log('Refreshing images...');
        fetchImages(); // Refresh the list
      } else {
        console.error('Upload failed:', result.error);
        alert(`Upload failed: ${result.error || 'Unknown error'}`);
      }

    } catch (error) {
      console.error('Upload error:', error);
      alert(`Upload failed: ${error.message}`);
    } finally {
      setUploading(false);
      console.log('=== UPLOAD DEBUG END ===');
    }
  };

  const handleDelete = async (filename) => {
    if (!confirm('Are you sure you want to delete this image?')) {
      return;
    }

    try {
      console.log('🗑️ Deleting image:', filename);
      const response = await fetch(`/api/gallery/simple-cloudinary?filename=${encodeURIComponent(filename)}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        alert('Image deleted successfully!');
        fetchImages(); // Refresh the list
      } else {
        alert(result.error || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete error:', error);
      alert('Delete failed');
    }
  };

  if (checkingAuth) {
    return (
      <div className="pt-20 pb-10 px-4 bg-gray-900 min-h-screen">
        <div className="max-w-7xl mx-auto text-center text-white">
          <p>Checking authentication...</p>
        </div>
      </div>
    );
  }

  if (!authenticated) {
    return null; // Will redirect to login
  }

  if (loading) {
    return (
      <div className="pt-20 pb-10 px-4 bg-gray-900 min-h-screen">
        <div className="max-w-7xl mx-auto text-center text-white">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 pb-10 px-4 bg-gray-900 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div className="text-center flex-1">
            <h1 className="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-amber-600 via-yellow-200 to-yellow-400">
              Gallery Admin
            </h1>
          </div>
          <button
            onClick={handleLogout}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors duration-200"
          >
            Logout
          </button>
        </div>
        <hr className="border-yellow-300 mb-8 w-1/4 mx-auto" />

        {/* Upload Form */}
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">Upload New Image</h2>

          {/* Debug Buttons - Commented out for production */}
          {/*
          <div className="flex gap-2 mb-4">
            <button
              onClick={() => console.log('🧪 Test button clicked!')}
              className="bg-blue-500 text-white px-4 py-2 rounded"
            >
              Test Console Log
            </button>
            <button
              onClick={() => {
                console.log('🔄 Manual refresh clicked!');
                fetchImages();
              }}
              className="bg-green-500 text-white px-4 py-2 rounded"
            >
              Refresh Images
            </button>
          </div>
          */}

          <form onSubmit={handleUpload} className="space-y-4">
            <div>
              <label htmlFor="image-file" className="block text-white mb-2">Image File</label>
              <input
                id="image-file"
                type="file"
                name="file"
                accept="image/*"
                required
                className="w-full p-2 rounded bg-gray-700 text-white"
              />
            </div>
            <div>
              <label htmlFor="image-title" className="block text-white mb-2">Title</label>
              <input
                id="image-title"
                type="text"
                name="title"
                value={uploadForm.title}
                onChange={(e) => setUploadForm({...uploadForm, title: e.target.value})}
                className="w-full p-2 rounded bg-gray-700 text-white"
                placeholder="Enter image title"
              />
            </div>
            <div>
              <label htmlFor="image-description" className="block text-white mb-2">Description</label>
              <textarea
                id="image-description"
                name="description"
                value={uploadForm.description}
                onChange={(e) => setUploadForm({...uploadForm, description: e.target.value})}
                className="w-full p-2 rounded bg-gray-700 text-white"
                rows="3"
                placeholder="Enter image description"
              />
            </div>
            <div>
              <label htmlFor="image-category" className="block text-white mb-2">Category</label>
              <select
                id="image-category"
                name="category"
                value={uploadForm.category}
                onChange={(e) => setUploadForm({...uploadForm, category: e.target.value})}
                className="w-full p-2 rounded bg-gray-700 text-white"
              >
                {categories.map(cat => (
                  <option key={cat} value={cat}>
                    {cat.charAt(0).toUpperCase() + cat.slice(1).replace('-', ' ')}
                  </option>
                ))}
              </select>
            </div>
            <button
              type="submit"
              disabled={uploading}
              className="bg-yellow-400 text-black px-6 py-2 rounded font-bold hover:bg-yellow-300 disabled:opacity-50"
            >
              {uploading ? 'Uploading...' : 'Upload Image'}
            </button>
          </form>
        </div>

        {/* Images Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {images.map((image) => (
            <div key={image.filename} className="bg-gray-800 rounded-lg overflow-hidden">
              <Image
                src={image.url}
                alt={image.title}
                width={300}
                height={200}
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <h3 className="text-white font-bold mb-2">{image.title}</h3>
                <p className="text-gray-400 text-sm mb-2">{image.description}</p>
                <p className="text-yellow-400 text-xs mb-3">Category: {image.category}</p>
                <button
                  onClick={() => handleDelete(image.filename)}
                  className="bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>

        {images.length === 0 && (
          <div className="text-center text-white">
            <p className="text-xl">No images uploaded yet.</p>
            <p className="text-gray-400">Use the form above to upload your first image.</p>
          </div>
        )}
      </div>
    </div>
  );
}
