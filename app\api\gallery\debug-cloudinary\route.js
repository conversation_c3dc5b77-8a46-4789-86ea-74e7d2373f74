import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    const debug = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      credentials: {
        cloudName: cloudName ? 'Set' : 'Missing',
        apiKey: apiKey ? 'Set' : 'Missing', 
        apiSecret: apiSecret ? 'Set' : 'Missing'
      }
    };

    if (!cloudName || !apiKey || !apiSecret) {
      return NextResponse.json({
        ...debug,
        error: 'Missing Cloudinary credentials'
      }, { status: 400 });
    }

    // Test basic API access
    try {
      const testUrl = `https://api.cloudinary.com/v1_1/${cloudName}/resources/image?max_results=10`;
      console.log('Testing Cloudinary URL:', testUrl);
      
      const response = await fetch(testUrl, {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`
        }
      });

      const responseText = await response.text();
      console.log('Cloudinary response status:', response.status);
      console.log('Cloudinary response:', responseText.substring(0, 500));

      if (response.ok) {
        const data = JSON.parse(responseText);
        debug.cloudinaryTest = {
          status: 'success',
          totalResources: data.resources?.length || 0,
          resources: data.resources?.slice(0, 3).map(r => ({
            public_id: r.public_id,
            url: r.secure_url,
            created_at: r.created_at
          })) || []
        };
      } else {
        debug.cloudinaryTest = {
          status: 'failed',
          httpStatus: response.status,
          error: responseText
        };
      }

    } catch (apiError) {
      debug.cloudinaryTest = {
        status: 'error',
        message: apiError.message
      };
    }

    return NextResponse.json(debug);

  } catch (error) {
    return NextResponse.json({
      error: 'Debug failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
