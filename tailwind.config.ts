import type { Config } from "tailwindcss";
import flowbite from "flowbite-react/tailwind";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./out/*.html",
    "/public/*.{svg,jpg,jpeg,png}",
    "./out/**/*.{js,ts,jsx,tsx,mdx}",
    "./out/*.{svg,jpg,jpeg,png}",
    "./node_modules/flowbite-react/lib/**/*.js",
    "./node_modules/flowbite/**/*.js",

    flowbite.content(),
  ],
  safelist: ["dark"],
  darkMode: "class",
  // ...
  theme: {
    extend: {
      backgroundImage: {
        edr: "url('/background.png')",
      },
      colors: {
        blue: {
          950: "#1F2937",
          900: "#1e3a8a",
        },
        black: "#000",
        yellow: {
          400: "#E3A008",
        },
      },
    },
  },
  plugins: [flowbite.plugin()],
};
export default config;
