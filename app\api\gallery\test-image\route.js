import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET(request) {
  console.log('=== TEST IMAGE API START ===');
  
  try {
    // Get the first image from KV to test
    let testImage = null;
    
    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      try {
        const kvResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        if (kvResponse.ok) {
          const kvData = await kvResponse.json();
          let kvImages = kvData.result || [];
          
          // Handle double-encoded JSON
          if (typeof kvImages === 'string') {
            kvImages = JSON.parse(kvImages);
          }

          if (Array.isArray(kvImages) && kvImages.length > 0) {
            testImage = kvImages[0];
          }
        }
      } catch (kvError) {
        console.error('KV error:', kvError);
      }
    }

    if (!testImage) {
      return NextResponse.json({
        error: 'No test image found',
        message: 'No images in KV storage to test'
      });
    }

    // Test if we can serve the image
    const imageUrl = `/api/gallery/image/${testImage.filename}`;
    
    const result = {
      testImage: {
        filename: testImage.filename,
        title: testImage.title,
        hasBase64: !!testImage.base64Data,
        base64Length: testImage.base64Data ? testImage.base64Data.length : 0,
        mimeType: testImage.mimeType || testImage.type
      },
      imageUrl: imageUrl,
      timestamp: new Date().toISOString()
    };

    console.log('Test image result:', result);
    console.log('=== TEST IMAGE API END ===');

    return NextResponse.json(result);

  } catch (error) {
    console.error('Test image API error:', error);
    return NextResponse.json({ 
      error: 'Test image API failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
