import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== DELETE ALL KV START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      operations: [],
      success: false
    };

    // Step 1: Get current KV data
    results.operations.push({ step: 1, operation: 'Get Current KV Data', status: 'start' });
    
    const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let currentImages = [];
    
    if (getResponse.ok) {
      const responseText = await getResponse.text();
      const kvData = JSON.parse(responseText);
      let kvResult = kvData.result || [];
      
      // Handle double-encoded JSON
      if (typeof kvResult === 'string') {
        try {
          currentImages = JSON.parse(kvResult);
        } catch (parseError) {
          currentImages = [];
        }
      } else if (Array.isArray(kvResult)) {
        currentImages = kvResult;
      }
      
      results.operations.push({ 
        step: 1, 
        operation: 'Get Current KV Data', 
        status: 'success',
        message: `Found ${currentImages.length} images in KV`,
        imageCount: currentImages.length
      });
      
      // Log all current images for debugging
      results.currentImages = currentImages.map(img => ({
        filename: img?.filename || 'unknown',
        title: img?.title || 'unknown',
        uploadDate: img?.uploadDate || 'unknown'
      }));
      
    } else {
      results.operations.push({ 
        step: 1, 
        operation: 'Get Current KV Data', 
        status: 'failed',
        message: `KV get failed: ${getResponse.status}`
      });
      return NextResponse.json(results);
    }

    // Step 2: Clear KV storage completely
    results.operations.push({ step: 2, operation: 'Clear KV Storage', status: 'start' });
    
    const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify([]), // Empty array
    });

    if (setResponse.ok) {
      results.operations.push({ 
        step: 2, 
        operation: 'Clear KV Storage', 
        status: 'success',
        message: 'KV storage cleared successfully'
      });
      
      // Step 3: Verify the clear
      results.operations.push({ step: 3, operation: 'Verify Clear', status: 'start' });
      
      const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (verifyResponse.ok) {
        const verifyText = await verifyResponse.text();
        const verifyData = JSON.parse(verifyText);
        let verifyResult = verifyData.result || [];
        
        if (typeof verifyResult === 'string') {
          try {
            verifyResult = JSON.parse(verifyResult);
          } catch (e) {
            verifyResult = [];
          }
        }
        
        const finalCount = Array.isArray(verifyResult) ? verifyResult.length : 0;
        
        results.operations.push({ 
          step: 3, 
          operation: 'Verify Clear', 
          status: 'success',
          message: `Verification complete: ${finalCount} images remaining`,
          finalCount: finalCount
        });
        
        if (finalCount === 0) {
          results.success = true;
          results.message = 'All images deleted successfully from KV!';
        } else {
          results.message = `Clear failed - ${finalCount} images still remain`;
        }
        
      } else {
        results.operations.push({ 
          step: 3, 
          operation: 'Verify Clear', 
          status: 'failed',
          message: `Verification failed: ${verifyResponse.status}`
        });
      }
      
    } else {
      const errorText = await setResponse.text();
      results.operations.push({ 
        step: 2, 
        operation: 'Clear KV Storage', 
        status: 'failed',
        message: `KV clear failed: ${setResponse.status} - ${errorText}`
      });
    }

    // Step 4: Clear global storage too
    results.operations.push({ step: 4, operation: 'Clear Global Storage', status: 'start' });
    
    try {
      global.galleryImages = [];
      results.operations.push({ 
        step: 4, 
        operation: 'Clear Global Storage', 
        status: 'success',
        message: 'Global storage cleared'
      });
    } catch (globalError) {
      results.operations.push({ 
        step: 4, 
        operation: 'Clear Global Storage', 
        status: 'failed',
        message: `Global clear failed: ${globalError.message}`
      });
    }

    console.log('=== DELETE ALL KV END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('Delete all KV error:', error);
    return NextResponse.json({
      error: 'Delete all failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
