import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== MANUAL KV DELETE START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      operations: [],
      success: false,
      finalVerification: null
    };

    // Method 1: Try DEL command (Redis DELETE)
    try {
      console.log('🔥 Method 1: Using DEL command...');
      
      const delResponse = await fetch(`${process.env.KV_REST_API_URL}/del/gallery_images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      const delResult = await delResponse.text();
      
      results.operations.push({
        method: 1,
        operation: 'DEL Command',
        status: delResponse.ok ? 'success' : 'failed',
        httpStatus: delResponse.status,
        response: delResult,
        message: delResponse.ok ? 'DEL command executed' : `DEL failed: ${delResponse.status}`
      });
      
      console.log(`Method 1 result: ${delResponse.status} - ${delResult}`);
    } catch (delError) {
      results.operations.push({
        method: 1,
        operation: 'DEL Command',
        status: 'error',
        message: delError.message
      });
    }

    // Method 2: Try setting to null
    try {
      console.log('🔥 Method 2: Setting to null...');
      
      const nullResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(null),
      });

      const nullResult = await nullResponse.text();
      
      results.operations.push({
        method: 2,
        operation: 'Set to NULL',
        status: nullResponse.ok ? 'success' : 'failed',
        httpStatus: nullResponse.status,
        response: nullResult,
        message: nullResponse.ok ? 'Set to null executed' : `Set null failed: ${nullResponse.status}`
      });
      
      console.log(`Method 2 result: ${nullResponse.status} - ${nullResult}`);
    } catch (nullError) {
      results.operations.push({
        method: 2,
        operation: 'Set to NULL',
        status: 'error',
        message: nullError.message
      });
    }

    // Method 3: Try FLUSHDB (if available)
    try {
      console.log('🔥 Method 3: Trying FLUSHDB...');
      
      const flushResponse = await fetch(`${process.env.KV_REST_API_URL}/flushdb`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      const flushResult = await flushResponse.text();
      
      results.operations.push({
        method: 3,
        operation: 'FLUSHDB',
        status: flushResponse.ok ? 'success' : 'failed',
        httpStatus: flushResponse.status,
        response: flushResult,
        message: flushResponse.ok ? 'FLUSHDB executed (clears entire DB!)' : `FLUSHDB failed: ${flushResponse.status}`
      });
      
      console.log(`Method 3 result: ${flushResponse.status} - ${flushResult}`);
    } catch (flushError) {
      results.operations.push({
        method: 3,
        operation: 'FLUSHDB',
        status: 'error',
        message: flushError.message
      });
    }

    // Method 4: Force overwrite with empty array
    try {
      console.log('🔥 Method 4: Force overwrite with empty array...');
      
      const overwriteResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify([]),
      });

      const overwriteResult = await overwriteResponse.text();
      
      results.operations.push({
        method: 4,
        operation: 'Force Overwrite Empty Array',
        status: overwriteResponse.ok ? 'success' : 'failed',
        httpStatus: overwriteResponse.status,
        response: overwriteResult,
        message: overwriteResponse.ok ? 'Empty array set' : `Overwrite failed: ${overwriteResponse.status}`
      });
      
      console.log(`Method 4 result: ${overwriteResponse.status} - ${overwriteResult}`);
    } catch (overwriteError) {
      results.operations.push({
        method: 4,
        operation: 'Force Overwrite Empty Array',
        status: 'error',
        message: overwriteError.message
      });
    }

    // Wait a moment for KV to process
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Final verification
    try {
      console.log('🔍 Final verification...');
      
      const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (verifyResponse.status === 404) {
        results.finalVerification = {
          status: 'success',
          message: 'Key not found (404) - successfully deleted!',
          imageCount: 0
        };
        results.success = true;
      } else if (verifyResponse.ok) {
        const verifyData = await verifyResponse.json();
        let images = verifyData.result || [];
        
        if (typeof images === 'string') {
          try {
            images = JSON.parse(images);
          } catch (e) {
            images = [];
          }
        }
        
        const imageCount = Array.isArray(images) ? images.length : 'unknown';
        results.finalVerification = {
          status: imageCount === 0 ? 'success' : 'failed',
          message: `Key exists with ${imageCount} images`,
          imageCount: imageCount,
          dataType: typeof verifyData.result
        };
        results.success = imageCount === 0;
      } else {
        results.finalVerification = {
          status: 'error',
          message: `Verification failed: ${verifyResponse.status}`,
          imageCount: 'unknown'
        };
      }
    } catch (verifyError) {
      results.finalVerification = {
        status: 'error',
        message: verifyError.message,
        imageCount: 'unknown'
      };
    }

    // Clear global storage regardless
    global.galleryImages = [];

    console.log('=== MANUAL KV DELETE END ===');
    console.log('Final success:', results.success);
    
    return NextResponse.json(results);

  } catch (error) {
    console.error('Manual KV delete error:', error);
    return NextResponse.json({
      error: 'Manual delete failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
