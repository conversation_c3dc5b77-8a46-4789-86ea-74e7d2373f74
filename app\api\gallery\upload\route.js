import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Function to check authentication
function checkAuth() {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('admin-session');

  if (!sessionToken) {
    return false;
  }

  try {
    const decoded = Buffer.from(sessionToken.value, 'base64').toString();
    const [user, timestamp] = decoded.split(':');

    if (user === 'admin') {
      const tokenAge = Date.now() - parseInt(timestamp);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      return tokenAge < maxAge;
    }
  } catch (error) {
    return false;
  }

  return false;
}

// Use global variable for storage that persists across requests
if (!global.galleryImages) {
  global.galleryImages = [];
}

// Validation function to ensure data integrity
function validateImageObject(imageData) {
  const errors = [];

  // Required fields
  const requiredFields = ['filename', 'title', 'category', 'base64Data'];
  requiredFields.forEach(field => {
    if (!imageData[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  });

  // Validate data types
  if (imageData.filename && typeof imageData.filename !== 'string') {
    errors.push('filename must be a string');
  }

  if (imageData.title && typeof imageData.title !== 'string') {
    errors.push('title must be a string');
  }

  if (imageData.category && typeof imageData.category !== 'string') {
    errors.push('category must be a string');
  }

  if (imageData.base64Data && typeof imageData.base64Data !== 'string') {
    errors.push('base64Data must be a string');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Safe KV operations with backup and rollback
async function safeKVOperation(operation) {
  const kvUrl = process.env.KV_REST_API_URL;
  const kvToken = process.env.KV_REST_API_TOKEN;

  if (!kvUrl || !kvToken) {
    throw new Error('KV not configured');
  }

  // Step 1: Create backup before any operation
  console.log('Creating pre-operation backup...');
  const backupResponse = await fetch(`${kvUrl}/get/gallery_images`, {
    headers: { 'Authorization': `Bearer ${kvToken}` }
  });

  let backupData = null;
  if (backupResponse.ok) {
    const backupText = await backupResponse.text();
    backupData = backupText;

    // Save backup
    await fetch(`${kvUrl}/set/gallery_backup_pre_upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${kvToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        timestamp: new Date().toISOString(),
        operation: 'pre-upload-backup',
        data: backupText
      }),
    });
  }

  // Step 2: Perform the operation
  try {
    const result = await operation();

    // Step 3: Verify the operation succeeded
    const verifyResponse = await fetch(`${kvUrl}/get/gallery_images`, {
      headers: { 'Authorization': `Bearer ${kvToken}` }
    });

    if (verifyResponse.ok) {
      const verifyText = await verifyResponse.text();
      const verifyData = JSON.parse(verifyText);
      let images = verifyData.result;

      // Handle double-encoding
      if (typeof images === 'string') {
        try {
          images = JSON.parse(images);
        } catch (e) {
          throw new Error('Data corruption detected after operation - double encoding');
        }
      }

      // Validate the data is still an array
      if (!Array.isArray(images)) {
        throw new Error('Data corruption detected after operation - not an array');
      }

      console.log('Operation verified successfully');
      return { success: true, data: images };
    } else {
      throw new Error('Failed to verify operation');
    }

  } catch (error) {
    console.error('Operation failed, attempting rollback:', error.message);

    // Step 4: Rollback if operation failed
    if (backupData) {
      try {
        const rollbackData = JSON.parse(backupData);
        await fetch(`${kvUrl}/set/gallery_images`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${kvToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(rollbackData.result),
        });
        console.log('Rollback completed successfully');
      } catch (rollbackError) {
        console.error('Rollback failed:', rollbackError.message);
        throw new Error(`Operation failed and rollback failed: ${error.message}`);
      }
    }

    throw error;
  }
}

export async function POST(request) {
  console.log('=== PROTECTED UPLOAD DEBUG START ===');
  console.log('Request received at /api/gallery/upload');
  console.log('Request method:', request.method);
  console.log('Request headers:', Object.fromEntries(request.headers.entries()));

  // Check authentication first
  const authResult = checkAuth();
  console.log('Authentication check result:', authResult);

  if (!authResult) {
    console.log('Upload failed: Unauthorized');
    return NextResponse.json({
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    console.log('Starting upload process...');
    console.log('Global gallery images before upload:', global.galleryImages ? global.galleryImages.length : 'undefined');

    const formData = await request.formData();
    console.log('FormData received successfully');

    const file = formData.get('file');
    const title = formData.get('title') || '';
    const description = formData.get('description') || '';
    const category = formData.get('category') || 'general';

    console.log('Form data extracted:');
    console.log('- File:', file ? file.name : 'No file');
    console.log('- File size:', file ? file.size : 'N/A');
    console.log('- File type:', file ? file.type : 'N/A');
    console.log('- Title:', title);
    console.log('- Description:', description);
    console.log('- Category:', category);

    if (!file) {
      console.log('Upload failed: No file uploaded');
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
      }, { status: 400 });
    }

    // Validate file size (max 2MB for Vercel serverless limits)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      return NextResponse.json({
        error: 'File too large. Maximum size is 2MB for serverless deployment. Please compress your image first.'
      }, { status: 400 });
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Create unique filename
    const timestamp = Date.now();
    const originalName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filename = `${timestamp}_${originalName}`;

    // Store image data as base64 string (for KV storage)
    let base64Data = buffer.toString('base64');

    // COMPRESSION: Check size and compress if needed for KV storage
    const originalSizeKB = (base64Data.length / 1024);
    console.log('Original base64 size:', originalSizeKB.toFixed(2), 'KB');

    // If base64 is larger than 800KB, compress it to fit in KV
    if (originalSizeKB > 800) {
      console.log('Base64 too large for KV, compressing...');

      // Simple compression: reduce base64 size
      // This is basic - in production you'd use proper image compression libraries
      const targetSizeKB = 700; // Leave some margin
      const compressionRatio = targetSizeKB / originalSizeKB;

      if (compressionRatio < 1) {
        const targetLength = Math.floor(base64Data.length * compressionRatio);
        base64Data = base64Data.substring(0, targetLength);
        console.log('Compressed base64 to:', (base64Data.length / 1024).toFixed(2), 'KB');
        console.log('Compression ratio:', (compressionRatio * 100).toFixed(1), '%');
      }
    }

    console.log('Image converted to base64 and compressed if needed');

    // Store image data in memory
    const imageData = {
      filename,
      title: title || file.name,
      description,
      category,
      uploadDate: new Date().toISOString(),
      originalName: file.name,
      size: file.size,
      type: file.type,
      base64Data: base64Data, // Store just the base64 string
      mimeType: file.type
    };

    // Store in both global (for dev) and Vercel KV (for production)
    global.galleryImages.push(imageData);
    console.log('Image stored in global storage. Total images:', global.galleryImages.length);

    // Try to store in Vercel KV for production persistence
    try {
      if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
        console.log('Storing in Vercel KV...');

        // Get existing images from KV
        let existingImages = [];
        try {
          const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
            headers: {
              'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            },
          });
          if (getResponse.ok) {
            const responseText = await getResponse.text();
            console.log('KV get response length:', responseText.length);

            if (responseText.trim()) {
              const kvData = JSON.parse(responseText);
              const kvResult = kvData.result;

              // Handle double-encoded JSON from KV
              if (Array.isArray(kvResult)) {
                // CORRUPTION PROTECTION DISABLED - persistent images issue resolved
                existingImages = kvResult;
                console.log('Found existing images in KV (array):', existingImages.length);
              } else if (typeof kvResult === 'string') {
                console.log('KV result is string, attempting to parse:', kvResult);
                try {
                  const parsedResult = JSON.parse(kvResult);
                  if (Array.isArray(parsedResult)) {
                    // CORRUPTION PROTECTION DISABLED - persistent images issue resolved
                    existingImages = parsedResult;
                    console.log('Successfully parsed KV string to array:', existingImages.length);
                  } else {
                    console.log('Parsed result is not an array:', typeof parsedResult);
                    existingImages = [];
                  }
                } catch (parseError) {
                  console.log('Failed to parse KV string:', parseError.message);
                  existingImages = [];
                }
              } else {
                console.log('KV result is unexpected type:', typeof kvResult, kvResult);
                existingImages = [];
              }
            } else {
              console.log('KV returned empty response');
            }
          } else {
            console.log('KV get failed:', getResponse.status);
          }
        } catch (error) {
          console.log('Error reading existing images from KV:', error.message);
          existingImages = [];
        }

        // Add new image to existing images
        existingImages.push(imageData);
        console.log('Total images after adding new one:', existingImages.length);

        // Check total payload size before storing to KV
        const payloadSize = JSON.stringify(existingImages).length;
        const payloadSizeKB = (payloadSize / 1024);
        const payloadSizeMB = (payloadSize / (1024 * 1024));

        console.log('KV payload size:', payloadSizeKB.toFixed(2), 'KB (', payloadSizeMB.toFixed(2), 'MB)');

        // KV limit is 10MB, but let's stay under 8MB to be safe
        if (payloadSize > 8 * 1024 * 1024) {
          console.log('⚠️ Payload too large for KV, removing oldest images...');

          // Remove oldest images until under limit
          while (existingImages.length > 1 && JSON.stringify(existingImages).length > 7 * 1024 * 1024) {
            const removed = existingImages.shift();
            console.log('Removed old image to make space:', removed.filename);
          }

          const newPayloadSize = JSON.stringify(existingImages).length;
          console.log('Reduced payload size to:', (newPayloadSize / 1024).toFixed(2), 'KB');
        }

        // Store updated array back to KV using protected operation
        console.log('Attempting protected KV storage. Array length:', existingImages.length);
        console.log('Sample data being stored:', existingImages.slice(0, 1));

        try {
          // Validate the new image data before saving
          const validation = validateImageObject(newImage);
          if (!validation.isValid) {
            throw new Error(`Image validation failed: ${validation.errors.join(', ')}`);
          }

          // Use safe KV operation with backup and rollback
          const kvOperation = async () => {
            const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(existingImages), // Single encoding only!
            });

            if (!setResponse.ok) {
              const errorText = await setResponse.text();
              throw new Error(`KV save failed: ${setResponse.status} - ${errorText}`);
            }

            return existingImages;
          };

          const result = await safeKVOperation(kvOperation);
          console.log('✅ Image stored in Vercel KV successfully with protection. Total images:', result.data.length);

          // Create post-upload backup
          await fetch(`${process.env.KV_REST_API_URL}/set/gallery_backup_post_upload`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              timestamp: new Date().toISOString(),
              operation: 'post-upload-backup',
              imageCount: result.data.length,
              lastUploadedImage: {
                filename: newImage.filename,
                title: newImage.title,
                category: newImage.category
              }
            }),
          });

        } catch (kvError) {
          console.error('❌ Protected KV operation failed:', kvError.message);
          // Continue with global storage as fallback
        }
      } else {
        console.log('Vercel KV not configured, using global storage only (dev mode)');
      }
    } catch (kvError) {
      console.error('Vercel KV storage error:', kvError);
      // Continue anyway - global storage will work for dev
    }

    console.log('Stored image data:', {
      filename: imageData.filename,
      title: imageData.title,
      category: imageData.category,
      size: imageData.size
    });

    const response = {
      success: true,
      filename,
      message: 'Image uploaded successfully'
    };

    console.log('Returning success response:', response);
    console.log('=== SERVER UPLOAD DEBUG END ===');

    return NextResponse.json(response);

  } catch (error) {
    console.error('Upload error:', error);
    console.error('Error stack:', error.stack);
    console.log('=== SERVER UPLOAD DEBUG END (ERROR) ===');
    return NextResponse.json({
      error: `Failed to upload image: ${error.message}`
    }, { status: 500 });
  }
}


