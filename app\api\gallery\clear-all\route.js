import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== CLEAR ALL STORAGE START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      operations: [],
      success: true,
      errors: []
    };

    // 1. Clear global storage
    try {
      global.galleryImages = [];
      results.operations.push({
        operation: 'Clear Global Storage',
        status: 'success',
        message: 'Global storage cleared'
      });
      console.log('✅ Global storage cleared');
    } catch (error) {
      results.operations.push({
        operation: 'Clear Global Storage',
        status: 'error',
        message: error.message
      });
      results.errors.push('Failed to clear global storage');
    }

    // 2. Clear KV storage
    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      try {
        console.log('Clearing KV storage...');
        
        // Set empty array in KV
        const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify([]), // Empty array
        });

        if (setResponse.ok) {
          results.operations.push({
            operation: 'Clear KV Storage',
            status: 'success',
            message: 'KV storage cleared successfully'
          });
          console.log('✅ KV storage cleared');

          // Verify the clear worked
          const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
            headers: {
              'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            },
          });

          if (verifyResponse.ok) {
            const verifyData = await verifyResponse.json();
            let verifyImages = verifyData.result || [];
            
            if (typeof verifyImages === 'string') {
              try {
                verifyImages = JSON.parse(verifyImages);
              } catch (e) {
                verifyImages = [];
              }
            }
            
            const imageCount = Array.isArray(verifyImages) ? verifyImages.length : 'unknown';
            results.operations.push({
              operation: 'Verify KV Clear',
              status: 'success',
              message: `KV now contains ${imageCount} images`
            });
            console.log(`✅ Verification: KV contains ${imageCount} images`);
          }
        } else {
          const errorText = await setResponse.text();
          results.operations.push({
            operation: 'Clear KV Storage',
            status: 'error',
            message: `Failed to clear KV: ${setResponse.status} - ${errorText}`
          });
          results.errors.push('Failed to clear KV storage');
          results.success = false;
        }
      } catch (kvError) {
        results.operations.push({
          operation: 'Clear KV Storage',
          status: 'error',
          message: kvError.message
        });
        results.errors.push('KV clear operation failed');
        results.success = false;
      }
    } else {
      results.operations.push({
        operation: 'Clear KV Storage',
        status: 'skipped',
        message: 'KV not configured'
      });
    }

    // 3. Clear any file system images (if they exist)
    try {
      const fs = require('fs').promises;
      const path = require('path');
      
      const directories = [
        path.join(process.cwd(), 'public', 'uploads'),
        path.join(process.cwd(), 'public', 'gallery')
      ];

      for (const dir of directories) {
        try {
          const files = await fs.readdir(dir);
          const imageFiles = files.filter(file => 
            /\.(jpg|jpeg|png|gif|webp)$/i.test(file)
          );

          for (const file of imageFiles) {
            await fs.unlink(path.join(dir, file));
          }

          if (imageFiles.length > 0) {
            results.operations.push({
              operation: `Clear ${dir}`,
              status: 'success',
              message: `Removed ${imageFiles.length} files`
            });
          }
        } catch (dirError) {
          // Directory might not exist, that's okay
          results.operations.push({
            operation: `Clear ${dir}`,
            status: 'skipped',
            message: 'Directory not found or empty'
          });
        }
      }
    } catch (fsError) {
      results.operations.push({
        operation: 'Clear File System',
        status: 'error',
        message: 'File system operations not available in serverless'
      });
    }

    console.log('=== CLEAR ALL STORAGE END ===');
    console.log('Results:', results);

    return NextResponse.json(results);

  } catch (error) {
    console.error('Clear all storage error:', error);
    return NextResponse.json({
      error: 'Clear operation failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
