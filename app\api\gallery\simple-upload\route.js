import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { writeFile, readFile, mkdir, unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Function to check authentication
function checkAuth() {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('admin-session');

  if (!sessionToken) {
    return false;
  }

  try {
    const decoded = Buffer.from(sessionToken.value, 'base64').toString();
    const [user, timestamp] = decoded.split(':');

    if (user === 'admin') {
      const tokenAge = Date.now() - parseInt(timestamp);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      return tokenAge < maxAge;
    }
  } catch (error) {
    return false;
  }

  return false;
}

// Ensure upload directory exists
async function ensureUploadDir() {
  const uploadDir = path.join(process.cwd(), 'public', 'gallery');
  if (!existsSync(uploadDir)) {
    await mkdir(uploadDir, { recursive: true });
  }
  return uploadDir;
}

// Load existing metadata
async function loadMetadata() {
  const metadataPath = path.join(process.cwd(), 'public', 'gallery', 'metadata.json');
  
  try {
    if (existsSync(metadataPath)) {
      const data = await readFile(metadataPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.log('Could not load metadata:', error.message);
  }
  
  return {};
}

// Save metadata
async function saveMetadata(metadata) {
  const metadataPath = path.join(process.cwd(), 'public', 'gallery', 'metadata.json');
  
  try {
    await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    
    // Also create a backup
    const backupPath = path.join(process.cwd(), 'public', 'gallery', `metadata_backup_${Date.now()}.json`);
    await writeFile(backupPath, JSON.stringify(metadata, null, 2));
    
    return true;
  } catch (error) {
    console.error('Failed to save metadata:', error);
    return false;
  }
}

export async function POST(request) {
  console.log('=== SIMPLE FILE UPLOAD START ===');

  try {
    // Check authentication
    if (!checkAuth()) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file');
    const title = formData.get('title') || 'Untitled';
    const description = formData.get('description') || '';
    const category = formData.get('category') || 'general';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
      }, { status: 400 });
    }

    // Validate file size (5MB max for file system)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({
        error: 'File too large. Maximum size is 5MB.'
      }, { status: 400 });
    }

    // Ensure upload directory exists
    const uploadDir = await ensureUploadDir();

    // Create unique filename
    const timestamp = Date.now();
    const originalName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filename = `${timestamp}_${originalName}`;
    const filePath = path.join(uploadDir, filename);

    // Save file to disk
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    console.log('✅ File saved to:', filePath);

    // Load existing metadata
    const metadata = await loadMetadata();

    // Add new image metadata
    const imageData = {
      filename,
      title,
      description,
      category,
      uploadDate: new Date().toISOString(),
      originalName: file.name,
      size: file.size,
      type: file.type,
      url: `/gallery/${filename}`,
      source: 'filesystem'
    };

    metadata[filename] = imageData;

    // Save updated metadata
    const saveSuccess = await saveMetadata(metadata);

    if (!saveSuccess) {
      return NextResponse.json({
        error: 'File uploaded but metadata save failed'
      }, { status: 500 });
    }

    console.log('✅ Image uploaded successfully to file system');

    return NextResponse.json({
      success: true,
      message: 'Image uploaded successfully to file system',
      image: {
        filename: imageData.filename,
        title: imageData.title,
        category: imageData.category,
        url: imageData.url,
        uploadDate: imageData.uploadDate
      }
    });

  } catch (error) {
    console.error('File upload failed:', error);
    return NextResponse.json({
      error: 'Upload failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET endpoint to retrieve images from file system
export async function GET() {
  try {
    const metadata = await loadMetadata();
    
    // Convert metadata object to array
    const images = Object.values(metadata).map(img => ({
      filename: img.filename,
      title: img.title,
      description: img.description,
      category: img.category,
      url: img.url,
      uploadDate: img.uploadDate,
      source: 'filesystem'
    }));

    // Sort by upload date (newest first)
    images.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));

    return NextResponse.json({
      success: true,
      images,
      totalCount: images.length,
      source: 'filesystem'
    });

  } catch (error) {
    console.error('Failed to retrieve images:', error);
    return NextResponse.json({
      error: 'Failed to retrieve images',
      details: error.message
    }, { status: 500 });
  }
}

// DELETE endpoint to remove images
export async function DELETE(request) {
  try {
    // Check authentication
    if (!checkAuth()) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const filename = searchParams.get('filename');

    if (!filename) {
      return NextResponse.json({ error: 'Filename required' }, { status: 400 });
    }

    // Load metadata
    const metadata = await loadMetadata();

    if (!metadata[filename]) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    // Delete file from disk
    const filePath = path.join(process.cwd(), 'public', 'gallery', filename);
    try {
      await unlink(filePath);
    } catch (error) {
      console.log('File may not exist on disk:', error.message);
    }

    // Remove from metadata
    delete metadata[filename];

    // Save updated metadata
    await saveMetadata(metadata);

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    });

  } catch (error) {
    console.error('Delete failed:', error);
    return NextResponse.json({
      error: 'Delete failed',
      details: error.message
    }, { status: 500 });
  }
}
