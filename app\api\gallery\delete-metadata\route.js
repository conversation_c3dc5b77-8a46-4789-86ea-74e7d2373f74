import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== DELETE METADATA FILES START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      operations: [],
      success: false,
      metadataFiles: []
    };

    try {
      const fs = require('fs').promises;
      const path = require('path');
      
      const directories = [
        { name: 'public/images', path: path.join(process.cwd(), 'public', 'images') },
        { name: 'public/uploads', path: path.join(process.cwd(), 'public', 'uploads') },
        { name: 'public/gallery', path: path.join(process.cwd(), 'public', 'gallery') }
      ];

      let totalDeleted = 0;
      
      for (const dir of directories) {
        try {
          const metadataPath = path.join(dir.path, 'metadata.json');
          console.log(`Checking for metadata file: ${metadataPath}`);
          
          // Check if metadata.json exists
          try {
            const stats = await fs.stat(metadataPath);
            console.log(`Found metadata.json in ${dir.name}, size: ${stats.size} bytes`);
            
            // Read the content first to see what's in it
            const content = await fs.readFile(metadataPath, 'utf8');
            let metadata = {};
            try {
              metadata = JSON.parse(content);
            } catch (parseError) {
              console.log('Metadata file is not valid JSON');
            }
            
            results.metadataFiles.push({
              directory: dir.name,
              path: metadataPath,
              size: stats.size,
              exists: true,
              content: Object.keys(metadata).length > 0 ? Object.keys(metadata) : 'Invalid JSON',
              imageCount: Object.keys(metadata).length
            });
            
            // Delete the metadata file
            await fs.unlink(metadataPath);
            console.log(`✅ Deleted metadata.json from ${dir.name}`);
            totalDeleted++;
            
            results.operations.push({
              operation: `Delete metadata.json from ${dir.name}`,
              status: 'success',
              message: `Deleted file with ${Object.keys(metadata).length} image entries`,
              path: metadataPath
            });
            
          } catch (statError) {
            if (statError.code === 'ENOENT') {
              console.log(`No metadata.json found in ${dir.name}`);
              results.operations.push({
                operation: `Check metadata.json in ${dir.name}`,
                status: 'not_found',
                message: 'No metadata.json file found'
              });
            } else {
              console.log(`Error accessing metadata.json in ${dir.name}:`, statError.message);
              results.operations.push({
                operation: `Check metadata.json in ${dir.name}`,
                status: 'error',
                message: statError.message
              });
            }
          }
          
        } catch (dirError) {
          console.log(`Error processing directory ${dir.name}:`, dirError.message);
          results.operations.push({
            operation: `Process directory ${dir.name}`,
            status: 'error',
            message: dirError.message
          });
        }
      }
      
      results.success = totalDeleted > 0;
      results.totalDeleted = totalDeleted;
      
      console.log(`✅ Deleted ${totalDeleted} metadata files`);
      
    } catch (fsError) {
      results.operations.push({
        operation: 'File System Access',
        status: 'error',
        message: 'File system operations limited in serverless environment'
      });
      console.log('File system error:', fsError.message);
    }

    // Also clear global storage and KV to be sure
    try {
      global.galleryImages = [];
      results.operations.push({
        operation: 'Clear Global Storage',
        status: 'success',
        message: 'Global storage cleared'
      });
    } catch (globalError) {
      results.operations.push({
        operation: 'Clear Global Storage',
        status: 'error',
        message: globalError.message
      });
    }

    // Set KV to empty array
    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      try {
        const kvResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify([]),
        });

        if (kvResponse.ok) {
          results.operations.push({
            operation: 'Reset KV Storage',
            status: 'success',
            message: 'KV set to empty array'
          });
        } else {
          results.operations.push({
            operation: 'Reset KV Storage',
            status: 'warning',
            message: `KV reset failed: ${kvResponse.status}`
          });
        }
      } catch (kvError) {
        results.operations.push({
          operation: 'Reset KV Storage',
          status: 'error',
          message: kvError.message
        });
      }
    }

    console.log('=== DELETE METADATA FILES END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('Delete metadata error:', error);
    return NextResponse.json({
      error: 'Delete metadata failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
