import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Function to check authentication
function checkAuth() {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('admin-session');

  if (!sessionToken) {
    return false;
  }

  try {
    const decoded = Buffer.from(sessionToken.value, 'base64').toString();
    const [user, timestamp] = decoded.split(':');

    if (user === 'admin') {
      const tokenAge = Date.now() - parseInt(timestamp);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      return tokenAge < maxAge;
    }
  } catch (error) {
    return false;
  }

  return false;
}

// No longer using memory storage - Cloudinary is the source of truth

export async function POST(request) {
  console.log('=== SIMPLE CLOUDINARY UPLOAD ===');

  try {
    // Check authentication
    if (!checkAuth()) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    if (!cloudName || !apiKey || !apiSecret) {
      return NextResponse.json({ 
        error: 'Cloudinary not configured' 
      }, { status: 400 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file');
    const title = formData.get('title') || 'Untitled';
    const description = formData.get('description') || '';
    const category = formData.get('category') || 'general';

    // Debug: Log what we received
    console.log('Form data received:');
    console.log('- file:', file ? file.name : 'No file');
    console.log('- title:', title);
    console.log('- description:', description);
    console.log('- category:', category);
    console.log('All form data keys:', Array.from(formData.keys()));

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Convert file to base64
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64Data = `data:${file.type};base64,${buffer.toString('base64')}`;

    // Create unique filename (remove extension to prevent double extension)
    const timestamp = Date.now();
    const originalName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
    const cleanName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filename = `${timestamp}_${cleanName}`;

    // Upload to Cloudinary using signed upload (more reliable)
    const uploadTimestamp = Math.round(Date.now() / 1000);
    const publicId = `gallery/${filename}`;

    // Generate signature (include context in signature)
    const crypto = require('crypto');
    const context = `title=${encodeURIComponent(title)}|description=${encodeURIComponent(description)}|category=${encodeURIComponent(category)}`;
    const paramsToSign = `context=${context}&public_id=${publicId}&timestamp=${uploadTimestamp}${apiSecret}`;
    const signature = crypto.createHash('sha1').update(paramsToSign).digest('hex');

    const uploadFormData = new FormData();
    uploadFormData.append('file', base64Data);
    uploadFormData.append('api_key', apiKey);
    uploadFormData.append('timestamp', uploadTimestamp.toString());
    uploadFormData.append('public_id', publicId);
    uploadFormData.append('signature', signature);
    uploadFormData.append('context', context);

    console.log('Uploading to Cloudinary with signed upload...');
    const uploadResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/upload`, {
      method: 'POST',
      body: uploadFormData,
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error('Cloudinary upload failed:', errorText);
      return NextResponse.json({
        error: 'Upload to Cloudinary failed',
        details: errorText
      }, { status: 500 });
    }

    const uploadResult = await uploadResponse.json();
    console.log('✅ Cloudinary upload successful');

    // Create image metadata
    const imageData = {
      filename,
      title,
      description,
      category,
      uploadDate: new Date().toISOString(),
      originalName: file.name,
      size: file.size,
      type: file.type,
      url: uploadResult.secure_url, // Direct Cloudinary URL
      cloudinary: {
        publicId: uploadResult.public_id,
        url: uploadResult.secure_url,
        width: uploadResult.width,
        height: uploadResult.height,
        format: uploadResult.format,
        bytes: uploadResult.bytes
      },
      source: 'cloudinary'
    };

    // Debug: Log the image data being stored
    console.log('Image data created:', {
      filename: imageData.filename,
      title: imageData.title,
      description: imageData.description,
      category: imageData.category
    });

    // No need to store in memory - Cloudinary is the source of truth
    console.log('✅ Image stored in Cloudinary, will be fetched dynamically');

    return NextResponse.json({
      success: true,
      message: 'Image uploaded successfully to Cloudinary',
      image: {
        filename: imageData.filename,
        title: imageData.title,
        category: imageData.category,
        url: imageData.url,
        uploadDate: imageData.uploadDate
      }
    });

  } catch (error) {
    console.error('Upload failed:', error);
    return NextResponse.json({
      error: 'Upload failed',
      details: error.message
    }, { status: 500 });
  }
}

// Function to get images directly from Cloudinary
async function getImagesFromCloudinary() {
  const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
  const apiKey = process.env.CLOUDINARY_API_KEY;
  const apiSecret = process.env.CLOUDINARY_API_SECRET;

  if (!cloudName || !apiKey || !apiSecret) {
    throw new Error('Cloudinary credentials not configured');
  }

  try {
    // Get all images from the gallery folder (include context metadata)
    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/resources/image?type=upload&prefix=gallery&max_results=500&context=true`, {
      headers: {
        'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Cloudinary API error:', response.status, errorText);
      throw new Error(`Cloudinary API failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    // Transform Cloudinary resources to our gallery format
    const images = data.resources.map(resource => {
      // Extract filename from public_id (remove "gallery/" prefix)
      const filename = resource.public_id.replace('gallery/', '');

      // Extract metadata from context if available
      let title = 'Untitled';
      let description = '';
      let category = 'general';

      if (resource.context && resource.context.custom) {
        // Parse context metadata
        if (resource.context.custom.title) {
          title = decodeURIComponent(resource.context.custom.title);
        }
        if (resource.context.custom.description) {
          description = decodeURIComponent(resource.context.custom.description);
        }
        if (resource.context.custom.category) {
          category = decodeURIComponent(resource.context.custom.category);
        }
      } else {
        // Fallback: Parse title from filename (remove timestamp prefix)
        const titleMatch = filename.match(/^\d+_(.+)$/);
        if (titleMatch) {
          title = titleMatch[1].replace(/[_-]/g, ' ').replace(/\.[^/.]+$/, ''); // Remove extension
        }
      }

      return {
        filename: filename,
        title: title,
        description: description,
        category: category,
        uploadDate: resource.created_at,
        url: resource.secure_url,
        width: resource.width,
        height: resource.height,
        size: resource.bytes,
        type: `image/${resource.format}`,
        cloudinary: {
          publicId: resource.public_id,
          url: resource.secure_url,
          width: resource.width,
          height: resource.height,
          format: resource.format,
          bytes: resource.bytes
        },
        source: 'cloudinary'
      };
    });

    // Sort by upload date (newest first)
    images.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));

    return images;

  } catch (error) {
    console.error('Failed to fetch from Cloudinary:', error);
    throw error;
  }
}

export async function GET() {
  try {
    // Always fetch from Cloudinary (source of truth)
    const images = await getImagesFromCloudinary();

    return NextResponse.json({
      success: true,
      images: images.map(img => ({
        filename: img.filename,
        title: img.title,
        description: img.description,
        category: img.category,
        url: img.url,
        uploadDate: img.uploadDate,
        source: img.source
      })),
      totalCount: images.length,
      storageMethod: 'cloudinary',
      persistent: true
    });

  } catch (error) {
    console.error('Failed to retrieve images:', error);
    return NextResponse.json({
      error: 'Failed to retrieve images',
      details: error.message,
      fallback: 'Cloudinary fetch failed'
    }, { status: 500 });
  }
}

export async function DELETE(request) {
  try {
    // Check authentication
    if (!checkAuth()) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const filename = searchParams.get('filename');

    if (!filename) {
      return NextResponse.json({ error: 'Filename required' }, { status: 400 });
    }

    const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    if (!cloudName || !apiKey || !apiSecret) {
      return NextResponse.json({
        error: 'Cloudinary not configured'
      }, { status: 400 });
    }

    // First, get the actual public_id from Cloudinary by finding the image
    let actualPublicId = null;

    try {
      // Get all images to find the correct public_id
      const listResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/resources/image?type=upload&prefix=gallery&max_results=500`, {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`
        }
      });

      if (listResponse.ok) {
        const listData = await listResponse.json();
        console.log('Searching for filename:', filename);
        console.log('Available images:', listData.resources.map(r => r.public_id));

        const targetImage = listData.resources.find(resource => {
          const resourceFilename = resource.public_id.replace('gallery/', '');
          console.log('Comparing:', resourceFilename, 'with', filename);
          // Try exact match first, then check if it starts with filename (for double extensions)
          return resourceFilename === filename ||
                 resourceFilename.startsWith(filename + '.') ||
                 resourceFilename.startsWith(filename.replace(/\.[^/.]+$/, '') + '.');
        });

        if (targetImage) {
          actualPublicId = targetImage.public_id;
          console.log('Found image to delete:', actualPublicId);
        } else {
          console.log('No matching image found for:', filename);
        }
      } else {
        console.log('Failed to list images:', listResponse.status);
      }
    } catch (listError) {
      console.error('Failed to list images:', listError);
      return NextResponse.json({
        error: 'Failed to list images from Cloudinary',
        details: listError.message,
        filename: filename
      }, { status: 500 });
    }

    // If we couldn't find the exact public_id, try the constructed one
    if (!actualPublicId) {
      actualPublicId = `gallery/${filename}`;
      console.log('Using constructed public_id:', actualPublicId);
    }

    try {
      // Delete from Cloudinary using signed request
      const timestamp = Math.round(Date.now() / 1000);
      const crypto = require('crypto');

      // Create signature for delete operation
      const paramsToSign = `public_id=${actualPublicId}&timestamp=${timestamp}${apiSecret}`;
      const signature = crypto.createHash('sha1').update(paramsToSign).digest('hex');

      const deleteResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/destroy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          public_id: actualPublicId,
          api_key: apiKey,
          timestamp: timestamp.toString(),
          signature: signature,
        }),
      });

      if (deleteResponse.ok) {
        const deleteResult = await deleteResponse.json();
        console.log('✅ Deleted from Cloudinary:', deleteResult);

        return NextResponse.json({
          success: true,
          message: 'Image deleted successfully from Cloudinary',
          cloudinaryResult: deleteResult,
          deletedPublicId: actualPublicId
        });
      } else {
        const errorText = await deleteResponse.text();
        console.error('Cloudinary deletion failed:', errorText);
        return NextResponse.json({
          error: 'Failed to delete from Cloudinary',
          details: errorText,
          attemptedPublicId: actualPublicId
        }, { status: 500 });
      }
    } catch (cloudinaryError) {
      console.error('Cloudinary deletion error:', cloudinaryError);
      return NextResponse.json({
        error: 'Cloudinary deletion failed',
        details: cloudinaryError.message,
        attemptedPublicId: actualPublicId
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Delete failed:', error);
    return NextResponse.json({
      error: 'Delete failed',
      details: error.message
    }, { status: 500 });
  }
}
