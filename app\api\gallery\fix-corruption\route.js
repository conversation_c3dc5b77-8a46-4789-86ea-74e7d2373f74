import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== FIX CORRUPTION API START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check (use your admin password)
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }
    
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    // Step 1: Get current valid images from global storage
    const validImages = global.galleryImages || [];
    console.log('Valid images in global storage:', validImages.length);

    // Step 2: Reset KV storage with only the valid images
    console.log('Resetting KV storage with valid images...');
    const resetResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(validImages),
    });

    if (!resetResponse.ok) {
      const errorText = await resetResponse.text();
      console.error('Failed to reset KV:', errorText);
      return NextResponse.json({
        error: 'Failed to reset KV storage',
        details: errorText
      }, { status: 500 });
    }

    // Step 3: Verify the fix worked
    const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let verifyCount = 'unknown';
    let verifySuccess = false;
    
    if (verifyResponse.ok) {
      const verifyData = await verifyResponse.json();
      let verifyImages = verifyData.result || [];
      
      if (typeof verifyImages === 'string') {
        try {
          verifyImages = JSON.parse(verifyImages);
        } catch (e) {
          verifyImages = [];
        }
      }
      
      verifyCount = Array.isArray(verifyImages) ? verifyImages.length : 'not array';
      verifySuccess = Array.isArray(verifyImages) && verifyImages.length === validImages.length;
    }

    const result = {
      success: true,
      message: 'Corruption fixed successfully',
      details: {
        validImagesPreserved: validImages.length,
        kvStorageReset: true,
        verificationPassed: verifySuccess,
        finalKvCount: verifyCount,
        timestamp: new Date().toISOString()
      }
    };

    console.log('Corruption fix complete:', result);
    console.log('=== FIX CORRUPTION API END ===');

    return NextResponse.json(result);

  } catch (error) {
    console.error('Fix corruption error:', error);
    return NextResponse.json({ 
      error: 'Fix corruption failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
