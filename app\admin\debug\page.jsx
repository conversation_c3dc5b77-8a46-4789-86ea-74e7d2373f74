'use client';

import { useState } from 'react';

export default function DebugPage() {
  const [debugInfo, setDebugInfo] = useState(null);
  const [testResult, setTestResult] = useState(null);
  const [cleanupResult, setCleanupResult] = useState(null);
  const [resetResult, setResetResult] = useState(null);
  const [forceResetResult, setForceResetResult] = useState(null);
  const [migrateResult, setMigrateResult] = useState(null);
  const [fixResult, setFixResult] = useState(null);
  const [healthCheck, setHealthCheck] = useState(null);
  const [kvTestResult, setKvTestResult] = useState(null);
  const [kvInspectResult, setKvInspectResult] = useState(null);
  const [kvSaveResult, setKvSaveResult] = useState(null);
  const [kvUploadTestResult, setKvUploadTestResult] = useState(null);
  const [deleteAllResult, setDeleteAllResult] = useState(null);
  const [forceClearResult, setForceClearResult] = useState(null);
  const [kvKeyInspectorResult, setKvKeyInspectorResult] = useState(null);
  const [directDeleteResult, setDirectDeleteResult] = useState(null);
  const [uploadTestResult, setUploadTestResult] = useState(null);
  const [clearAllResult, setClearAllResult] = useState(null);
  const [nuclearResetResult, setNuclearResetResult] = useState(null);
  const [traceResult, setTraceResult] = useState(null);
  const [manualDeleteResult, setManualDeleteResult] = useState(null);
  const [deleteMetadataResult, setDeleteMetadataResult] = useState(null);
  const [targetedDeleteResult, setTargetedDeleteResult] = useState(null);
  const [debugApiResult, setDebugApiResult] = useState(null);
  const [uploadDebugResult, setUploadDebugResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [showForceResetConfirm, setShowForceResetConfirm] = useState(false);
  const [showClearAllConfirm, setShowClearAllConfirm] = useState(false);
  const [showNuclearConfirm, setShowNuclearConfirm] = useState(false);
  const [showManualDeleteConfirm, setShowManualDeleteConfirm] = useState(false);
  const [showDeleteMetadataConfirm, setShowDeleteMetadataConfirm] = useState(false);
  const [showTargetedDeleteConfirm, setShowTargetedDeleteConfirm] = useState(false);
  const [uploadTestFile, setUploadTestFile] = useState(null);

  const runDebug = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/debug');
      const data = await response.json();
      setDebugInfo(data);
    } catch (error) {
      console.error('Debug failed:', error);
      setDebugInfo({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testImage = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/test-image');
      const data = await response.json();
      setTestResult(data);
    } catch (error) {
      console.error('Test failed:', error);
      setTestResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const cleanupKV = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-cleanup', { method: 'POST' });
      const data = await response.json();
      setCleanupResult(data);
    } catch (error) {
      console.error('Cleanup failed:', error);
      setCleanupResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const resetKV = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setResetResult(data);
      setShowResetConfirm(false);
    } catch (error) {
      console.error('Reset failed:', error);
      setResetResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const runHealthCheck = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/health-check');
      const data = await response.json();
      setHealthCheck(data);
    } catch (error) {
      console.error('Health check failed:', error);
      setHealthCheck({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const fixCorruption = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/fix-corruption', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setFixResult(data);
    } catch (error) {
      console.error('Fix corruption failed:', error);
      setFixResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const forceResetKV = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-force-reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setForceResetResult(data);
      setShowForceResetConfirm(false);
    } catch (error) {
      console.error('Force reset failed:', error);
      setForceResetResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const migrateKV = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-migrate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setMigrateResult(data);
    } catch (error) {
      console.error('Migration failed:', error);
      setMigrateResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testKVConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-test');
      const data = await response.json();
      setKvTestResult(data);
    } catch (error) {
      console.error('KV test failed:', error);
      setKvTestResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const inspectKV = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-inspect');
      const data = await response.json();
      setKvInspectResult(data);
    } catch (error) {
      console.error('KV inspect failed:', error);
      setKvInspectResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testUpload = async () => {
    if (!uploadTestFile) {
      alert('Please select a file first');
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', uploadTestFile);
      formData.append('title', 'Debug Test Upload');

      const response = await fetch('/api/gallery/upload-test', {
        method: 'POST',
        body: formData,
      });
      const data = await response.json();
      setUploadTestResult(data);
    } catch (error) {
      console.error('Upload test failed:', error);
      setUploadTestResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const clearAllStorage = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/clear-all', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setClearAllResult(data);
      setShowClearAllConfirm(false);
    } catch (error) {
      console.error('Clear all failed:', error);
      setClearAllResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const nuclearReset = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/nuclear-reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setNuclearResetResult(data);
      setShowNuclearConfirm(false);
    } catch (error) {
      console.error('Nuclear reset failed:', error);
      setNuclearResetResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const traceImages = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/trace-images');
      const data = await response.json();
      setTraceResult(data);
    } catch (error) {
      console.error('Trace failed:', error);
      setTraceResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const manualKVDelete = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/manual-kv-delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setManualDeleteResult(data);
      setShowManualDeleteConfirm(false);
    } catch (error) {
      console.error('Manual delete failed:', error);
      setManualDeleteResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const deleteMetadata = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/delete-metadata', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setDeleteMetadataResult(data);
      setShowDeleteMetadataConfirm(false);
    } catch (error) {
      console.error('Delete metadata failed:', error);
      setDeleteMetadataResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const targetedDelete = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/targeted-delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setTargetedDeleteResult(data);
      setShowTargetedDeleteConfirm(false);
    } catch (error) {
      console.error('Targeted delete failed:', error);
      setTargetedDeleteResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const debugApi = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/debug-api');
      const data = await response.json();
      setDebugApiResult(data);
    } catch (error) {
      console.error('Debug API failed:', error);
      setDebugApiResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const debugUpload = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/upload-debug', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setUploadDebugResult(data);
    } catch (error) {
      console.error('Upload debug failed:', error);
      setUploadDebugResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const inspectKVDetailed = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-inspect-detailed', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setKvInspectResult(data);
    } catch (error) {
      console.error('KV inspect failed:', error);
      setKvInspectResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const saveKVInspection = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-inspect-save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setKvSaveResult(data);
    } catch (error) {
      console.error('KV save failed:', error);
      setKvSaveResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testKVUpload = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/test-kv-upload', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setKvUploadTestResult(data);
    } catch (error) {
      console.error('KV upload test failed:', error);
      setKvUploadTestResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const deleteAllImages = async () => {
    if (!confirm('⚠️ DELETE ALL IMAGES? This will permanently remove ALL images from KV storage. This cannot be undone!')) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/gallery/delete-all-kv', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setDeleteAllResult(data);
    } catch (error) {
      console.error('Delete all failed:', error);
      setDeleteAllResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const forceClearKV = async () => {
    if (!confirm('🚨 FORCE CLEAR KV? This bypasses all logic and directly sets KV to empty. Use this if other deletes failed!')) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/gallery/force-clear-kv', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setForceClearResult(data);
    } catch (error) {
      console.error('Force clear failed:', error);
      setForceClearResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const inspectKVKeys = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gallery/kv-key-inspector', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setKvKeyInspectorResult(data);
    } catch (error) {
      console.error('KV key inspector failed:', error);
      setKvKeyInspectorResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const directDeleteTestImage = async () => {
    if (!confirm('🎯 DIRECT DELETE? This will specifically target and remove the test_1749770530809_kv_upload.webp image from KV.')) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/gallery/direct-kv-delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: 'ExpressRenos2024!' })
      });
      const data = await response.json();
      setDirectDeleteResult(data);
    } catch (error) {
      console.error('Direct delete failed:', error);
      setDirectDeleteResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-yellow-400">
          Gallery Debug Tools
        </h1>

        <div className="grid gap-6">
          {/* KV Upload Test - WHY UPLOADS DON'T PERSIST */}
          <div className="bg-purple-900 border border-purple-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-purple-300">🧪 KV UPLOAD TEST - WHY UPLOADS DON&apos;T PERSIST</h2>
            <p className="text-purple-200 mb-4">
              <strong>PERSISTENCE PROBLEM:</strong> Your uploads disappear after git push because they&apos;re not saving to KV properly.
              This test will show us exactly why KV saves are failing.
            </p>
            <button
              onClick={testKVUpload}
              disabled={loading}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : '🧪 TEST KV UPLOAD'}
            </button>

            {kvUploadTestResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  kvUploadTestResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {kvUploadTestResult.success ? '✅ KV Upload Working!' : '❌ KV Upload Failed'}
                  </div>
                  {kvUploadTestResult.message && (
                    <div className="mt-2 text-sm">{kvUploadTestResult.message}</div>
                  )}
                </div>

                {kvUploadTestResult.steps && (
                  <div className="mb-4">
                    <strong className="text-purple-200">🔬 Test Steps:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {kvUploadTestResult.steps.map((step, i) => (
                        <li key={i} className="text-sm text-purple-200">
                          Step {step.step}: {step.action} - {step.status || 'in progress'}
                          {step.error && <span className="text-red-300"> (Error: {step.error})</span>}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Detailed Test Results</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(kvUploadTestResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Targeted Image Deletion - PRECISION STRIKE */}
          <div className="bg-red-900 border border-red-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-red-300">🎯 TARGETED IMAGE DELETION - PRECISION STRIKE</h2>
            <p className="text-red-200 mb-4">
              <strong>PRECISION SOLUTION:</strong> This targets the exact 4 persistent images by their
              titles and descriptions that you provided. It will hunt them down and destroy them specifically.
            </p>
            <div className="bg-red-800 border border-red-500 p-3 rounded mb-4">
              <p className="text-red-200 text-sm">
                🎯 <strong>TARGETS:</strong>
                <br />• &quot;Bathroom Shower Renovation, 2025&quot;
                <br />• &quot;Basement&quot;
                <br />• &quot;test&quot; (both instances)
                <br />• Any images with filenames containing &quot;1749511630674_flooring_2.png&quot; or &quot;1749511944989_flooring_2.png&quot;
              </p>
            </div>

            {!showTargetedDeleteConfirm ? (
              <button
                onClick={() => setShowTargetedDeleteConfirm(true)}
                disabled={loading}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
              >
                🎯 TARGET & DESTROY 4 IMAGES
              </button>
            ) : (
              <div className="space-x-4">
                <button
                  onClick={targetedDelete}
                  disabled={loading}
                  className="bg-red-700 text-white px-4 py-2 rounded hover:bg-red-800 disabled:opacity-50"
                >
                  {loading ? 'Targeting...' : '🎯 CONFIRM TARGETED DELETION'}
                </button>
                <button
                  onClick={() => setShowTargetedDeleteConfirm(false)}
                  disabled={loading}
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            )}

            {targetedDeleteResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  targetedDeleteResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {targetedDeleteResult.success ? '✅ Targeted Deletion Complete!' : '❌ Targeted Deletion Failed'}
                  </div>
                  {targetedDeleteResult.imagesDeleted && (
                    <div className="mt-2">
                      <strong>Images Destroyed:</strong> {targetedDeleteResult.imagesDeleted.length}
                      <br />
                      <strong>Final Image Count:</strong> {targetedDeleteResult.finalCount}
                    </div>
                  )}
                </div>

                {targetedDeleteResult.imagesFound && targetedDeleteResult.imagesFound.length > 0 && (
                  <div className="mb-4">
                    <strong className="text-red-200">🎯 Targets Found:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {targetedDeleteResult.imagesFound.map((img, i) => (
                        <li key={i} className="text-sm text-red-200">
                          &quot;{img.title}&quot; - {img.filename}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {targetedDeleteResult.imagesDeleted && targetedDeleteResult.imagesDeleted.length > 0 && (
                  <div className="mb-4">
                    <strong className="text-green-200">🗑️ Images Deleted:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {targetedDeleteResult.imagesDeleted.map((img, i) => (
                        <li key={i} className="text-sm text-green-200">
                          &quot;{img.title}&quot; - {img.filename}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Detailed Results</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(targetedDeleteResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Debug API Test - What is the API actually returning? */}
          <div className="bg-blue-900 border border-blue-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-blue-300">🔍 DEBUG API TEST</h2>
            <p className="text-blue-200 mb-4">
              <strong>MYSTERY SOLVER:</strong> KV shows 0 images but gallery shows 4.
              This tests what your /api/gallery/images endpoint is actually returning to your frontend.
            </p>
            <button
              onClick={debugApi}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : '🔍 TEST IMAGES API'}
            </button>

            {debugApiResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  debugApiResult.test?.apiWorking ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {debugApiResult.test?.apiWorking ? '✅ API Working' : '❌ API Failed'}
                  </div>
                  {debugApiResult.analysis && (
                    <div className="mt-2 space-y-1 text-sm">
                      <div><strong>Status:</strong> {debugApiResult.test?.status}</div>
                      <div><strong>Image Count:</strong> {debugApiResult.analysis.imageCount}</div>
                      <div><strong>Issue:</strong> {debugApiResult.analysis.likelyIssue}</div>
                    </div>
                  )}
                </div>

                {debugApiResult.test?.sampleImages && debugApiResult.test.sampleImages.length > 0 && (
                  <div className="mb-4">
                    <strong className="text-blue-200">🖼️ Sample Images Returned:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {debugApiResult.test.sampleImages.map((img, i) => (
                        <li key={i} className="text-sm text-blue-200">
                          &quot;{img.title}&quot; - {img.filename} (source: {img.source})
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Full API Response</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(debugApiResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Upload Debug Test - Why aren't uploads saving to KV? */}
          <div className="bg-purple-900 border border-purple-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-purple-300">🔧 UPLOAD DEBUG TEST</h2>
            <p className="text-purple-200 mb-4">
              <strong>KV SAVE ISSUE:</strong> Uploads save to global storage but not KV.
              This tests the KV write functionality to find why uploads aren&apos;t persisting.
            </p>
            <button
              onClick={debugUpload}
              disabled={loading}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : '🔧 DEBUG KV UPLOAD'}
            </button>

            {uploadDebugResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  uploadDebugResult.error ? 'bg-red-800 border border-red-500' : 'bg-green-800 border border-green-500'
                }`}>
                  <div className="font-bold">
                    {uploadDebugResult.error ? '❌ KV Upload Test Failed' : '✅ KV Upload Test Complete'}
                  </div>
                  {uploadDebugResult.error && (
                    <div className="mt-1 text-sm">
                      Error: {uploadDebugResult.error}
                    </div>
                  )}
                </div>

                {uploadDebugResult.kvConfig && (
                  <div className="mb-4">
                    <strong className="text-purple-200">🔧 KV Configuration:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                      <li className="text-purple-200">Has URL: {uploadDebugResult.kvConfig.hasUrl ? '✅' : '❌'}</li>
                      <li className="text-purple-200">Has Token: {uploadDebugResult.kvConfig.hasToken ? '✅' : '❌'}</li>
                      <li className="text-purple-200">URL Preview: {uploadDebugResult.kvConfig.urlPreview}</li>
                    </ul>
                  </div>
                )}

                {uploadDebugResult.currentKvData && (
                  <div className="mb-4">
                    <strong className="text-purple-200">📊 Current KV Data:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                      <li className="text-purple-200">Image Count: {uploadDebugResult.currentKvData.imageCount}</li>
                      <li className="text-purple-200">Raw Data Length: {uploadDebugResult.currentKvData.rawLength} chars</li>
                    </ul>
                  </div>
                )}

                {uploadDebugResult.steps && uploadDebugResult.steps.length > 0 && (
                  <div className="mb-4">
                    <strong className="text-purple-200">🔍 Test Steps:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {uploadDebugResult.steps.map((step, i) => (
                        <li key={i} className={`text-sm ${
                          step.status === 'success' ? 'text-green-300' : 'text-red-300'
                        }`}>
                          Step {step.step}: {step.operation} - {step.message}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Full Debug Results</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(uploadDebugResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Detailed KV Inspection - Step by step KV parsing */}
          <div className="bg-orange-900 border border-orange-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-orange-300">🔬 DETAILED KV INSPECTION</h2>
            <p className="text-orange-200 mb-4">
              <strong>PARSING MYSTERY:</strong> Upload debug shows 6 images in KV, but API shows 1.
              This inspects every step of KV data parsing to find where the 5 images are lost.
            </p>
            <div className="space-x-4">
              <button
                onClick={inspectKVDetailed}
                disabled={loading}
                className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 disabled:opacity-50"
              >
                {loading ? 'Inspecting...' : '🔬 INSPECT KV PARSING'}
              </button>
              <button
                onClick={saveKVInspection}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : '💾 SAVE TO FILE'}
              </button>
            </div>

            {kvInspectResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  kvInspectResult.error ? 'bg-red-800 border border-red-500' : 'bg-green-800 border border-green-500'
                }`}>
                  <div className="font-bold">
                    {kvInspectResult.error ? '❌ KV Inspection Failed' : '✅ KV Inspection Complete'}
                  </div>
                  {kvInspectResult.finalImageCount !== undefined && (
                    <div className="mt-1 text-sm">
                      Final Image Count: {kvInspectResult.finalImageCount}
                    </div>
                  )}
                </div>

                {kvInspectResult.parsingSteps && kvInspectResult.parsingSteps.length > 0 && (
                  <div className="mb-4">
                    <strong className="text-orange-200">🔍 Parsing Steps:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {kvInspectResult.parsingSteps.map((step, i) => (
                        <li key={i} className={`text-sm ${
                          step.status === 'success' ? 'text-green-300' :
                          step.status === 'skipped' ? 'text-gray-300' : 'text-red-300'
                        }`}>
                          Step {step.step}: {step.operation} - {step.message}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {kvInspectResult.imageAnalysis && kvInspectResult.imageAnalysis.length > 0 && (
                  <div className="mb-4">
                    <strong className="text-orange-200">📊 Image Analysis:</strong>
                    <div className="mt-2 space-y-1 text-sm">
                      {kvInspectResult.imageAnalysis.slice(0, 10).map((img, i) => (
                        <div key={i} className={`${img.isValidObject ? 'text-green-300' : 'text-red-300'}`}>
                          #{i}: {img.filename} - {img.title} {img.isValidObject ? '✅' : '❌'}
                        </div>
                      ))}
                      {kvInspectResult.imageAnalysis.length > 10 && (
                        <div className="text-gray-300">... and {kvInspectResult.imageAnalysis.length - 10} more</div>
                      )}
                    </div>
                  </div>
                )}

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Full Inspection Results</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(kvInspectResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}

            {kvSaveResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  kvSaveResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {kvSaveResult.success ? '✅ File Saved Successfully!' : '❌ File Save Failed'}
                  </div>
                  {kvSaveResult.fileUrl && (
                    <div className="mt-2">
                      <a
                        href={kvSaveResult.fileUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-300 hover:text-blue-200 underline"
                      >
                        📁 Download: {kvSaveResult.fileUrl}
                      </a>
                    </div>
                  )}
                  {kvSaveResult.summary && (
                    <div className="mt-2 text-sm">
                      <strong>Summary:</strong> {kvSaveResult.summary.totalImages} total, {kvSaveResult.summary.validImages} valid, {kvSaveResult.summary.invalidImages} invalid
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Nuclear Option - DELETE ALL IMAGES */}
          <div className="bg-red-900 border border-red-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-red-300">☢️ NUCLEAR OPTION - DELETE ALL IMAGES</h2>
            <p className="text-red-200 mb-4">
              <strong>LAST RESORT:</strong> If the persistent image cannot be deleted normally, this will completely clear KV storage.
              This will delete ALL images including the stubborn &quot;Bathroom Shower Renovation, 2025&quot; image.
            </p>
            <div className="space-x-4">
              <button
                onClick={deleteAllImages}
                disabled={loading}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
              >
                {loading ? 'Deleting...' : '☢️ DELETE ALL IMAGES'}
              </button>
              <button
                onClick={forceClearKV}
                disabled={loading}
                className="bg-red-800 text-white px-4 py-2 rounded hover:bg-red-900 disabled:opacity-50"
              >
                {loading ? 'Forcing...' : '🚨 FORCE CLEAR KV'}
              </button>
              <button
                onClick={inspectKVKeys}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Inspecting...' : '🔍 INSPECT KV KEYS'}
              </button>
              <button
                onClick={directDeleteTestImage}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? 'Deleting...' : '🎯 DIRECT DELETE TEST IMAGE'}
              </button>
              <button
                onClick={async () => {
                  setLoading(true);
                  try {
                    const response = await fetch('/api/gallery/final-kv-diagnosis', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ password: 'ExpressRenos2024!' })
                    });
                    const data = await response.json();
                    setDirectDeleteResult(data);
                  } catch (error) {
                    console.error('Final diagnosis failed:', error);
                    setDirectDeleteResult({ error: error.message });
                  } finally {
                    setLoading(false);
                  }
                }}
                disabled={loading}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
              >
                {loading ? 'Diagnosing...' : '🔬 FINAL KV DIAGNOSIS & DELETE'}
              </button>
            </div>

            {deleteAllResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  deleteAllResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {deleteAllResult.success ? '✅ All Images Deleted!' : '❌ Delete Failed'}
                  </div>
                  {deleteAllResult.message && (
                    <div className="mt-2 text-sm">{deleteAllResult.message}</div>
                  )}
                </div>

                {deleteAllResult.currentImages && (
                  <div className="mb-4">
                    <strong className="text-red-200">📋 Images Found Before Delete:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {deleteAllResult.currentImages.map((img, i) => (
                        <li key={i} className="text-sm text-red-200">
                          <strong>{img.title}</strong> ({img.filename}) - {img.uploadDate}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Detailed Delete Results</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(deleteAllResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}

            {forceClearResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  forceClearResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {forceClearResult.success ? '✅ KV Force Cleared!' : '❌ Force Clear Failed'}
                  </div>
                  {forceClearResult.message && (
                    <div className="mt-2 text-sm">{forceClearResult.message}</div>
                  )}
                </div>

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Force Clear Details</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(forceClearResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}

            {kvKeyInspectorResult && (
              <div className="mt-4">
                <div className="bg-blue-800 border border-blue-500 p-3 rounded mb-4">
                  <div className="font-bold text-blue-200">🔍 KV Keys Found:</div>
                  {kvKeyInspectorResult.summary && (
                    <div className="mt-2 text-sm">
                      <div>Keys Checked: {kvKeyInspectorResult.summary.totalKeysChecked}</div>
                      <div>Existing Keys: {kvKeyInspectorResult.summary.existingKeys}</div>
                      <div>Keys with Images: {kvKeyInspectorResult.summary.keysWithImages}</div>
                      {kvKeyInspectorResult.summary.keysWithImageNames?.length > 0 && (
                        <div className="mt-2 font-bold text-yellow-300">
                          🚨 FOUND IMAGES IN: {kvKeyInspectorResult.summary.keysWithImageNames.join(', ')}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 All KV Keys Details</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(kvKeyInspectorResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}

            {directDeleteResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  directDeleteResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {directDeleteResult.success ? '✅ Test Image Deleted!' : '❌ Direct Delete Failed'}
                  </div>
                  {directDeleteResult.message && (
                    <div className="mt-2 text-sm">{directDeleteResult.message}</div>
                  )}
                </div>

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Direct Delete Details</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(directDeleteResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Delete Metadata Files - THE REAL SOLUTION */}
          <div className="bg-green-900 border border-green-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-green-300">🎯 DELETE METADATA FILES - FINAL SOLUTION</h2>
            <p className="text-green-200 mb-4">
              <strong>THIS IS THE REAL CULPRIT:</strong> Your trace shows metadata.json exists in public/images.
              This file contains the 4 persistent images&apos; data. When KV is empty, your API loads from this file!
            </p>
            <div className="bg-green-800 border border-green-500 p-3 rounded mb-4">
              <p className="text-green-200 text-sm">
                ✅ <strong>SOLUTION:</strong> Delete all metadata.json files from public directories.
                This should finally eliminate the 4 persistent images.
              </p>
            </div>

            {!showDeleteMetadataConfirm ? (
              <button
                onClick={() => setShowDeleteMetadataConfirm(true)}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
              >
                🎯 DELETE METADATA FILES
              </button>
            ) : (
              <div className="space-x-4">
                <button
                  onClick={deleteMetadata}
                  disabled={loading}
                  className="bg-green-700 text-white px-4 py-2 rounded hover:bg-green-800 disabled:opacity-50"
                >
                  {loading ? 'Deleting...' : '🎯 CONFIRM DELETE METADATA'}
                </button>
                <button
                  onClick={() => setShowDeleteMetadataConfirm(false)}
                  disabled={loading}
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            )}

            {deleteMetadataResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  deleteMetadataResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {deleteMetadataResult.success ? '✅ Metadata Files Deleted!' : '❌ Delete Failed'}
                  </div>
                  {deleteMetadataResult.totalDeleted !== undefined && (
                    <div className="mt-1">
                      Deleted {deleteMetadataResult.totalDeleted} metadata files
                    </div>
                  )}
                </div>

                {deleteMetadataResult.metadataFiles && deleteMetadataResult.metadataFiles.length > 0 && (
                  <div className="mb-4">
                    <strong className="text-green-200">Metadata Files Found:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {deleteMetadataResult.metadataFiles.map((file, i) => (
                        <li key={i} className="text-sm text-green-200">
                          {file.directory}: {file.imageCount} images ({file.size} bytes)
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {deleteMetadataResult.operations && (
                  <div className="mb-4">
                    <strong className="text-green-200">Operations:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {deleteMetadataResult.operations.map((op, i) => (
                        <li key={i} className={`text-sm ${
                          op.status === 'success' ? 'text-green-300' :
                          op.status === 'not_found' ? 'text-gray-300' : 'text-red-300'
                        }`}>
                          {op.operation} - {op.message}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Detailed Results</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(deleteMetadataResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Image Source Tracer - CRITICAL for diagnosing persistent images */}
          <div className="bg-purple-900 border border-purple-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-purple-300">🔍 IMAGE SOURCE TRACER</h2>
            <p className="text-purple-200 mb-4">
              <strong>CRITICAL DIAGNOSTIC:</strong> This traces exactly where the 4 persistent images
              are coming from - KV storage, file system, global memory, or API cache.
            </p>
            <button
              onClick={traceImages}
              disabled={loading}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? 'Tracing...' : '🔍 TRACE IMAGE SOURCES'}
            </button>

            {traceResult && (
              <div className="mt-4">
                <div className="bg-purple-800 border border-purple-500 p-3 rounded mb-4">
                  <div className="font-bold text-purple-200">📊 Analysis Results:</div>
                  {traceResult.analysis && (
                    <div className="mt-2 space-y-1 text-sm">
                      <div>KV Images: <span className="font-bold">{traceResult.analysis.kvImages}</span></div>
                      <div>Global Images: <span className="font-bold">{traceResult.analysis.globalImages}</span></div>
                      <div>File System Images: <span className="font-bold">{traceResult.analysis.filesystemImages}</span></div>
                      <div>API Images: <span className="font-bold">{traceResult.analysis.apiImages}</span></div>
                      <div className="mt-2 font-bold text-yellow-300">
                        Likely Source: {traceResult.analysis.likelySource}
                      </div>
                    </div>
                  )}
                  {traceResult.recommendations && traceResult.recommendations.length > 0 && (
                    <div className="mt-3">
                      <div className="font-bold text-purple-200">💡 Recommendations:</div>
                      <ul className="list-disc list-inside mt-1 text-sm">
                        {traceResult.recommendations.map((rec, i) => (
                          <li key={i} className="text-purple-200">{rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Detailed Trace Data</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(traceResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Manual KV Delete - Based on Trace Results */}
          <div className="bg-yellow-900 border border-yellow-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-yellow-300">🔥 MANUAL KV DELETION</h2>
            <p className="text-yellow-200 mb-4">
              <strong>BASED ON TRACE RESULTS:</strong> The 4 images are stuck in KV storage.
              This uses multiple deletion methods (DEL, NULL, FLUSHDB, Overwrite) to force-remove them.
            </p>
            <div className="bg-yellow-800 border border-yellow-500 p-3 rounded mb-4">
              <p className="text-yellow-200 text-sm">
                ⚠️ <strong>WARNING:</strong> FLUSHDB will clear your ENTIRE KV database (all keys).
                Only use this if the 4 images won&apos;t delete with other methods.
              </p>
            </div>

            {!showManualDeleteConfirm ? (
              <button
                onClick={() => setShowManualDeleteConfirm(true)}
                disabled={loading}
                className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 disabled:opacity-50"
              >
                🔥 MANUAL KV DELETE
              </button>
            ) : (
              <div className="space-x-4">
                <button
                  onClick={manualKVDelete}
                  disabled={loading}
                  className="bg-yellow-700 text-white px-4 py-2 rounded hover:bg-yellow-800 disabled:opacity-50"
                >
                  {loading ? 'Deleting...' : '🔥 CONFIRM MANUAL DELETE'}
                </button>
                <button
                  onClick={() => setShowManualDeleteConfirm(false)}
                  disabled={loading}
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            )}

            {manualDeleteResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  manualDeleteResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {manualDeleteResult.success ? '✅ Manual Delete Successful!' : '❌ Manual Delete Failed'}
                  </div>
                  {manualDeleteResult.finalVerification && (
                    <div className="mt-2">
                      <strong>Final Result:</strong> {manualDeleteResult.finalVerification.message}
                      <br />
                      <strong>Images Remaining:</strong> {manualDeleteResult.finalVerification.imageCount}
                    </div>
                  )}
                </div>

                {manualDeleteResult.operations && (
                  <div className="mb-4">
                    <strong className="text-yellow-200">Methods Tried:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {manualDeleteResult.operations.map((op, i) => (
                        <li key={i} className={`text-sm ${
                          op.status === 'success' ? 'text-green-300' :
                          op.status === 'failed' ? 'text-red-300' : 'text-yellow-300'
                        }`}>
                          Method {op.method}: {op.operation} - {op.message}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <details className="bg-gray-700 rounded">
                  <summary className="p-3 cursor-pointer font-bold">🔬 Detailed Results</summary>
                  <pre className="p-4 text-sm overflow-auto">
                    {JSON.stringify(manualDeleteResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Super Nuclear Reset - For Persistent 4 Images Issue */}
          <div className="bg-red-900 border border-red-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-red-300">💥 SUPER NUCLEAR RESET - For Persistent Images</h2>
            <p className="text-red-200 mb-4">
              <strong>UPDATED FOR YOUR ISSUE:</strong> This now destroys KV storage AND deletes all image files
              from the file system (public/images, public/uploads, public/gallery). The 4 persistent images
              are stored as actual files, not just in KV!
            </p>
            <div className="bg-red-800 border border-red-500 p-3 rounded mb-4">
              <p className="text-red-200 text-sm">
                ⚠️ <strong>WARNING:</strong> This will permanently delete ALL images from both KV storage
                AND file system. This should definitely eliminate the 4 persistent test images.
              </p>
            </div>

            {!showNuclearConfirm ? (
              <button
                onClick={() => setShowNuclearConfirm(true)}
                disabled={loading}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
              >
                💥 SUPER NUCLEAR RESET
              </button>
            ) : (
              <div className="space-x-4">
                <button
                  onClick={nuclearReset}
                  disabled={loading}
                  className="bg-red-800 text-white px-4 py-2 rounded hover:bg-red-900 disabled:opacity-50"
                >
                  {loading ? 'Super Nuking...' : '💥 CONFIRM SUPER NUCLEAR RESET'}
                </button>
                <button
                  onClick={() => setShowNuclearConfirm(false)}
                  disabled={loading}
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            )}

            {nuclearResetResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  nuclearResetResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {nuclearResetResult.success ? '✅ Super Nuclear Reset Complete!' : '❌ Super Nuclear Reset Failed'}
                  </div>
                  {nuclearResetResult.operations && (
                    <div className="mt-2">
                      <strong>Operations:</strong>
                      <ul className="list-disc list-inside mt-1">
                        {nuclearResetResult.operations.map((op, i) => (
                          <li key={i} className={`text-sm ${
                            op.status === 'success' ? 'text-green-300' :
                            op.status === 'error' ? 'text-red-300' : 'text-yellow-300'
                          }`}>
                            Step {op.step}: {op.operation} - {op.message}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {nuclearResetResult.errors?.length > 0 && (
                    <div className="mt-2">
                      <strong>Errors:</strong>
                      <ul className="list-disc list-inside">
                        {nuclearResetResult.errors.map((error, i) => (
                          <li key={i} className="text-red-300">{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(nuclearResetResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* KV Connection Test - Most Important */}
          <div className="bg-blue-900 border border-blue-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-blue-300">🔌 KV Connection Test</h2>
            <p className="text-blue-200 mb-4">
              <strong>START HERE:</strong> Test if your Vercel KV database is properly connected and working.
            </p>
            <button
              onClick={testKVConnection}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : '🧪 Test KV Connection'}
            </button>

            {kvTestResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  kvTestResult.test?.status === 'success' ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {kvTestResult.test?.status === 'success' ? '✅ KV Connection Working!' : '❌ KV Connection Failed'}
                  </div>
                  {kvTestResult.test?.message && (
                    <div className="mt-1">{kvTestResult.test.message}</div>
                  )}
                  {kvTestResult.test?.imageCount !== undefined && (
                    <div className="mt-1">Images in KV: {kvTestResult.test.imageCount}</div>
                  )}
                </div>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(kvTestResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Upload Test */}
          <div className="bg-purple-900 border border-purple-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-purple-300">📤 Upload Test</h2>
            <p className="text-purple-200 mb-4">
              Test the upload functionality directly to see if images can be stored in KV.
            </p>
            <div className="space-y-4">
              <div>
                <label className="block text-purple-200 mb-2">Select test image:</label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => setUploadTestFile(e.target.files[0])}
                  className="w-full p-2 rounded bg-gray-700 text-white"
                />
              </div>
              <button
                onClick={testUpload}
                disabled={loading || !uploadTestFile}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
              >
                {loading ? 'Uploading...' : '🚀 Test Upload'}
              </button>
            </div>

            {uploadTestResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  uploadTestResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {uploadTestResult.success ? '✅ Upload Test Successful!' : '❌ Upload Test Failed'}
                  </div>
                  {uploadTestResult.message && (
                    <div className="mt-1">{uploadTestResult.message}</div>
                  )}
                  {uploadTestResult.totalImages && (
                    <div className="mt-1">Total images after upload: {uploadTestResult.totalImages}</div>
                  )}
                </div>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(uploadTestResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* KV Data Inspector */}
          <div className="bg-yellow-900 border border-yellow-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-yellow-300">🔍 KV Data Inspector</h2>
            <p className="text-yellow-200 mb-4">
              Inspect the raw data in your KV storage to see what&apos;s actually stored.
            </p>
            <button
              onClick={inspectKV}
              disabled={loading}
              className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 disabled:opacity-50"
            >
              {loading ? 'Inspecting...' : '🔬 Inspect KV Data'}
            </button>

            {kvInspectResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  kvInspectResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {kvInspectResult.success ? '✅ KV Data Retrieved' : '❌ KV Inspection Failed'}
                  </div>
                  {kvInspectResult.kvResponse && (
                    <div className="mt-1">
                      Status: {kvInspectResult.kvResponse.status} |
                      Type: {kvInspectResult.kvResponse.resultType} |
                      Array: {kvInspectResult.kvResponse.resultIsArray ? 'Yes' : 'No'}
                    </div>
                  )}
                </div>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(kvInspectResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Clear All Storage - For Persistent Image Issue */}
          <div className="bg-orange-900 border border-orange-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-orange-300">🧹 Clear All Storage</h2>
            <p className="text-orange-200 mb-4">
              <strong>FOR YOUR ISSUE:</strong> This will completely clear all storage (KV, global, file system)
              to fix the persistent 4 test images that keep appearing when you delete everything.
            </p>

            {!showClearAllConfirm ? (
              <button
                onClick={() => setShowClearAllConfirm(true)}
                disabled={loading}
                className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 disabled:opacity-50"
              >
                🧹 Clear All Storage
              </button>
            ) : (
              <div className="space-x-4">
                <button
                  onClick={clearAllStorage}
                  disabled={loading}
                  className="bg-orange-700 text-white px-4 py-2 rounded hover:bg-orange-800 disabled:opacity-50"
                >
                  {loading ? 'Clearing...' : '✅ Confirm Clear All'}
                </button>
                <button
                  onClick={() => setShowClearAllConfirm(false)}
                  disabled={loading}
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            )}

            {clearAllResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  clearAllResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {clearAllResult.success ? '✅ All Storage Cleared!' : '❌ Clear Failed'}
                  </div>
                  {clearAllResult.operations && (
                    <div className="mt-2">
                      <strong>Operations:</strong>
                      <ul className="list-disc list-inside mt-1">
                        {clearAllResult.operations.map((op, i) => (
                          <li key={i} className={`text-sm ${
                            op.status === 'success' ? 'text-green-300' :
                            op.status === 'error' ? 'text-red-300' : 'text-gray-300'
                          }`}>
                            {op.operation}: {op.message}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {clearAllResult.errors?.length > 0 && (
                    <div className="mt-2">
                      <strong>Errors:</strong>
                      <ul className="list-disc list-inside">
                        {clearAllResult.errors.map((error, i) => (
                          <li key={i} className="text-red-300">{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(clearAllResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Quick Fix - Most Important */}
          <div className="bg-green-900 border border-green-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-green-300">🚀 Quick Fix Corruption</h2>
            <p className="text-green-200 mb-4">
              <strong>RECOMMENDED:</strong> This will preserve your recent uploads and fix the KV corruption.
              Your uploaded images should appear immediately after this fix.
            </p>
            <button
              onClick={fixCorruption}
              disabled={loading}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Fixing...' : '🔧 Fix Corruption & Preserve Uploads'}
            </button>

            {fixResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  fixResult.success ? 'bg-green-800 border border-green-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {fixResult.success ? '✅ Corruption Fixed!' : '❌ Fix Failed'}
                  </div>
                  {fixResult.message && (
                    <div className="mt-1">{fixResult.message}</div>
                  )}
                </div>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(fixResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Health Check */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4">🏥 Gallery Health Check</h2>
            <p className="text-gray-300 mb-4">
              Monitors for corruption, performance issues, and data integrity.
            </p>
            <button
              onClick={runHealthCheck}
              disabled={loading}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Checking...' : '🔍 Run Health Check'}
            </button>

            {healthCheck && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  healthCheck.status === 'healthy' ? 'bg-green-900 border border-green-600' :
                  healthCheck.status === 'warning' ? 'bg-yellow-900 border border-yellow-600' :
                  'bg-red-900 border border-red-600'
                }`}>
                  <div className="font-bold">
                    Status: {healthCheck.status === 'healthy' ? '✅ Healthy' :
                            healthCheck.status === 'warning' ? '⚠️ Warning' : '🚨 Unhealthy'}
                  </div>
                  {healthCheck.errors?.length > 0 && (
                    <div className="mt-2">
                      <strong>Errors:</strong>
                      <ul className="list-disc list-inside">
                        {healthCheck.errors.map((error, i) => (
                          <li key={i} className="text-red-300">{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {healthCheck.warnings?.length > 0 && (
                    <div className="mt-2">
                      <strong>Warnings:</strong>
                      <ul className="list-disc list-inside">
                        {healthCheck.warnings.map((warning, i) => (
                          <li key={i} className="text-yellow-300">{warning}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(healthCheck, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Debug Info */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4">System Debug Info</h2>
            <button
              onClick={runDebug}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Run Debug Check'}
            </button>

            {debugInfo && (
              <pre className="mt-4 bg-gray-700 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            )}
          </div>

          {/* Test Image */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4">Test Image Serving</h2>
            <button
              onClick={testImage}
              disabled={loading}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Test Image Access'}
            </button>
            
            {testResult && (
              <div className="mt-4">
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(testResult, null, 2)}
                </pre>
                
                {testResult.imageUrl && !testResult.error && (
                  <div className="mt-4">
                    <p className="mb-2">Test Image:</p>
                    <img 
                      src={testResult.imageUrl} 
                      alt="Test" 
                      className="max-w-xs border border-gray-600 rounded"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'block';
                      }}
                    />
                    <div style={{display: 'none'}} className="text-red-400 mt-2">
                      ❌ Image failed to load
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* KV Cleanup */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4">KV Storage Cleanup</h2>
            <p className="text-gray-300 mb-4">
              This will clean up corrupted images in KV storage and fix data format issues.
            </p>
            <button
              onClick={cleanupKV}
              disabled={loading}
              className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Clean Up KV Storage'}
            </button>

            {cleanupResult && (
              <pre className="mt-4 bg-gray-700 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(cleanupResult, null, 2)}
              </pre>
            )}
          </div>

          {/* KV Reset - Emergency */}
          <div className="bg-red-900 border border-red-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-red-300">🚨 Emergency KV Reset</h2>
            <p className="text-red-200 mb-4">
              <strong>WARNING:</strong> This will completely delete all images from KV storage.
              Only use this if the data is severely corrupted (like showing 1M+ images).
            </p>

            <div className="space-y-4">
              {/* Regular Reset */}
              <div>
                <h3 className="font-bold mb-2">Regular Reset:</h3>
                {!showResetConfirm ? (
                  <button
                    onClick={() => setShowResetConfirm(true)}
                    disabled={loading}
                    className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
                  >
                    🚨 Reset KV Storage
                  </button>
                ) : (
                  <div className="space-x-4">
                    <button
                      onClick={resetKV}
                      disabled={loading}
                      className="bg-red-700 text-white px-4 py-2 rounded hover:bg-red-800 disabled:opacity-50"
                    >
                      {loading ? 'Resetting...' : '✅ Confirm Reset'}
                    </button>
                    <button
                      onClick={() => setShowResetConfirm(false)}
                      disabled={loading}
                      className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                    >
                      Cancel
                    </button>
                  </div>
                )}
              </div>

              {/* Force Reset */}
              <div className="border-t border-red-600 pt-4">
                <h3 className="font-bold mb-2 text-red-200">🔥 NUCLEAR OPTION - Force Reset:</h3>
                <p className="text-red-300 text-sm mb-2">
                  If regular reset fails, this deletes the KV key entirely and recreates it.
                </p>
                {!showForceResetConfirm ? (
                  <button
                    onClick={() => setShowForceResetConfirm(true)}
                    disabled={loading}
                    className="bg-red-800 text-white px-4 py-2 rounded hover:bg-red-900 disabled:opacity-50"
                  >
                    🔥 FORCE RESET KV
                  </button>
                ) : (
                  <div className="space-x-4">
                    <button
                      onClick={forceResetKV}
                      disabled={loading}
                      className="bg-red-900 text-white px-4 py-2 rounded hover:bg-red-950 disabled:opacity-50"
                    >
                      {loading ? 'Force Resetting...' : '💥 CONFIRM FORCE RESET'}
                    </button>
                    <button
                      onClick={() => setShowForceResetConfirm(false)}
                      disabled={loading}
                      className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                    >
                      Cancel
                    </button>
                  </div>
                )}
              </div>
            </div>

            {resetResult && (
              <div className="mt-4">
                <h4 className="font-bold mb-2">Regular Reset Result:</h4>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(resetResult, null, 2)}
                </pre>
              </div>
            )}

            {forceResetResult && (
              <div className="mt-4">
                <h4 className="font-bold mb-2">Force Reset Result:</h4>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(forceResetResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* KV Migration - Last Resort */}
          <div className="bg-purple-900 border border-purple-600 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4 text-purple-300">🔄 KV Migration (Last Resort)</h2>
            <p className="text-purple-200 mb-4">
              <strong>LAST RESORT:</strong> If all resets fail, this creates a completely new KV key
              and abandons the corrupted one. This should definitely work.
            </p>
            <button
              onClick={migrateKV}
              disabled={loading}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? 'Migrating...' : '🔄 Migrate to New KV Key'}
            </button>

            {migrateResult && (
              <div className="mt-4">
                <div className={`p-3 rounded mb-4 ${
                  migrateResult.success ? 'bg-purple-800 border border-purple-500' : 'bg-red-800 border border-red-500'
                }`}>
                  <div className="font-bold">
                    {migrateResult.success ? '✅ Migration Successful!' : '❌ Migration Failed'}
                  </div>
                  {migrateResult.message && (
                    <div className="mt-1">{migrateResult.message}</div>
                  )}
                  {migrateResult.success && (
                    <div className="mt-2 text-purple-200">
                      <strong>Next:</strong> Code needs to be updated to use the new key name.
                    </div>
                  )}
                </div>
                <pre className="bg-gray-700 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(migrateResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Quick Links */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4">Quick Links & Direct API Access</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-bold text-gray-300 mb-2">🔧 New Diagnostic Tools:</h3>
                <div className="space-y-2">
                  <a
                    href="/api/gallery/kv-test"
                    target="_blank"
                    className="block text-blue-400 hover:text-blue-300"
                  >
                    🧪 KV Connection Test
                  </a>
                  <a
                    href="/api/gallery/kv-inspect"
                    target="_blank"
                    className="block text-blue-400 hover:text-blue-300"
                  >
                    🔍 KV Data Inspector
                  </a>
                  <a
                    href="/api/gallery/test"
                    target="_blank"
                    className="block text-blue-400 hover:text-blue-300"
                  >
                    ⚡ Gallery Test API
                  </a>
                </div>
              </div>
              <div>
                <h3 className="font-bold text-gray-300 mb-2">📊 Existing APIs:</h3>
                <div className="space-y-2">
                  <a
                    href="/api/gallery/images"
                    target="_blank"
                    className="block text-blue-400 hover:text-blue-300"
                  >
                    📋 Images API Response
                  </a>
                  <a
                    href="/api/gallery/images-debug"
                    target="_blank"
                    className="block text-blue-400 hover:text-blue-300"
                  >
                    🔍 Images Debug API
                  </a>
                  <a
                    href="/api/gallery/kv-inspect-raw"
                    target="_blank"
                    className="block text-blue-400 hover:text-blue-300"
                  >
                    🔬 KV Raw Data Inspector
                  </a>
                  <a
                    href="/api/gallery/images-simple"
                    target="_blank"
                    className="block text-blue-400 hover:text-blue-300"
                  >
                    🔍 Simple Images API Test
                  </a>
                </div>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-600">
              <a
                href="/admin/gallery"
                className="block text-yellow-400 hover:text-yellow-300 font-bold"
              >
                🖼️ Back to Gallery Admin
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
