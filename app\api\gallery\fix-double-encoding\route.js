import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST() {
  console.log('=== FIXING DOUBLE ENCODING ISSUE ===');
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        details: 'Missing KV_REST_API_URL or KV_REST_API_TOKEN'
      }, { status: 400 });
    }

    // Step 1: Get the current data
    console.log('Fetching current KV data...');
    const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (!getResponse.ok) {
      return NextResponse.json({
        error: 'Failed to fetch current data',
        status: getResponse.status
      }, { status: 500 });
    }

    const rawText = await getResponse.text();
    console.log('Raw KV response length:', rawText.length);
    console.log('Raw KV response preview:', rawText.substring(0, 200));

    let kvData;
    try {
      kvData = JSON.parse(rawText);
    } catch (parseError) {
      return NextResponse.json({
        error: 'Failed to parse KV response',
        details: parseError.message,
        rawResponse: rawText.substring(0, 500)
      }, { status: 500 });
    }

    console.log('KV data keys:', Object.keys(kvData));
    console.log('Result type:', typeof kvData.result);
    console.log('Result is array:', Array.isArray(kvData.result));

    let images = kvData.result;

    // Step 2: Check if it's double-encoded (string instead of array)
    if (typeof images === 'string') {
      console.log('Detected double-encoded JSON string, attempting to parse...');
      try {
        images = JSON.parse(images);
        console.log('Successfully parsed double-encoded JSON');
        console.log('Parsed images type:', typeof images);
        console.log('Parsed images is array:', Array.isArray(images));
        console.log('Parsed images length:', Array.isArray(images) ? images.length : 'N/A');
      } catch (doubleParseError) {
        return NextResponse.json({
          error: 'Failed to parse double-encoded JSON',
          details: doubleParseError.message,
          stringData: images.substring(0, 500)
        }, { status: 500 });
      }
    }

    // Step 3: Validate the data
    if (!Array.isArray(images)) {
      return NextResponse.json({
        error: 'Data is not an array after parsing',
        dataType: typeof images,
        data: images
      }, { status: 500 });
    }

    console.log(`Found ${images.length} images in KV storage`);

    // Step 4: Re-save the data properly (single-encoded)
    console.log('Re-saving data with proper encoding...');
    const saveResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(images), // Single encoding
    });

    if (!saveResponse.ok) {
      const errorText = await saveResponse.text();
      return NextResponse.json({
        error: 'Failed to save corrected data',
        status: saveResponse.status,
        details: errorText
      }, { status: 500 });
    }

    console.log('Successfully fixed double encoding issue');

    // Step 5: Verify the fix
    const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let verificationResult = 'Unknown';
    if (verifyResponse.ok) {
      const verifyText = await verifyResponse.text();
      const verifyData = JSON.parse(verifyText);
      verificationResult = {
        type: typeof verifyData.result,
        isArray: Array.isArray(verifyData.result),
        length: Array.isArray(verifyData.result) ? verifyData.result.length : 'N/A'
      };
    }

    return NextResponse.json({
      success: true,
      message: 'Double encoding issue fixed successfully',
      originalDataType: 'string (double-encoded)',
      fixedDataType: 'array',
      imagesRecovered: images.length,
      verification: verificationResult,
      imagesSample: images.slice(0, 2).map(img => ({
        filename: img.filename,
        title: img.title,
        category: img.category,
        uploadDate: img.uploadDate
      }))
    });

  } catch (error) {
    console.error('Fix double encoding failed:', error);
    return NextResponse.json({
      error: 'Fix operation failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
