import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== TRACE IMAGES START ===');
  
  const trace = {
    timestamp: new Date().toISOString(),
    sources: {},
    analysis: {},
    recommendations: []
  };

  try {
    // 1. Check KV Storage
    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      try {
        console.log('🔍 Checking KV storage...');
        
        const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        if (response.ok) {
          const responseText = await response.text();
          const kvData = JSON.parse(responseText);
          let images = kvData.result || [];
          
          if (typeof images === 'string') {
            try {
              images = JSON.parse(images);
            } catch (e) {
              images = [];
            }
          }
          
          trace.sources.kv = {
            status: 'success',
            imageCount: Array.isArray(images) ? images.length : 0,
            rawDataType: typeof kvData.result,
            rawDataLength: typeof kvData.result === 'string' ? kvData.result.length : 'N/A',
            sampleImages: Array.isArray(images) ? images.slice(0, 2).map(img => ({
              filename: img.filename,
              title: img.title,
              uploadDate: img.uploadDate,
              hasBase64: !!img.base64Data,
              base64Length: img.base64Data ? img.base64Data.length : 0
            })) : []
          };
          
          console.log(`✅ KV: Found ${trace.sources.kv.imageCount} images`);
        } else {
          trace.sources.kv = {
            status: 'error',
            message: `HTTP ${response.status}: ${response.statusText}`
          };
        }
      } catch (kvError) {
        trace.sources.kv = {
          status: 'error',
          message: kvError.message
        };
      }
    } else {
      trace.sources.kv = {
        status: 'not_configured',
        message: 'KV environment variables not set'
      };
    }

    // 2. Check Global Storage
    try {
      console.log('🔍 Checking global storage...');
      
      const globalImages = global.galleryImages || [];
      trace.sources.global = {
        status: 'success',
        imageCount: Array.isArray(globalImages) ? globalImages.length : 0,
        type: typeof globalImages,
        sampleImages: Array.isArray(globalImages) ? globalImages.slice(0, 2).map(img => ({
          filename: img?.filename,
          title: img?.title,
          uploadDate: img?.uploadDate
        })) : []
      };
      
      console.log(`✅ Global: Found ${trace.sources.global.imageCount} images`);
    } catch (globalError) {
      trace.sources.global = {
        status: 'error',
        message: globalError.message
      };
    }

    // 3. Check File System
    try {
      console.log('🔍 Checking file system...');
      
      const fs = require('fs').promises;
      const path = require('path');
      
      const directories = [
        { name: 'public/images', path: path.join(process.cwd(), 'public', 'images') },
        { name: 'public/uploads', path: path.join(process.cwd(), 'public', 'uploads') },
        { name: 'public/gallery', path: path.join(process.cwd(), 'public', 'gallery') }
      ];

      trace.sources.filesystem = {};
      
      for (const dir of directories) {
        try {
          const files = await fs.readdir(dir.path);
          const imageFiles = files.filter(file => 
            /\.(jpg|jpeg|png|gif|webp)$/i.test(file)
          );
          
          trace.sources.filesystem[dir.name] = {
            status: 'success',
            imageCount: imageFiles.length,
            files: imageFiles.slice(0, 5), // First 5 files
            hasMetadata: files.includes('metadata.json')
          };
          
          console.log(`✅ ${dir.name}: Found ${imageFiles.length} images`);
        } catch (dirError) {
          trace.sources.filesystem[dir.name] = {
            status: 'not_found',
            message: dirError.message
          };
          console.log(`❌ ${dir.name}: ${dirError.message}`);
        }
      }
    } catch (fsError) {
      trace.sources.filesystem = {
        status: 'error',
        message: 'File system access limited in serverless environment'
      };
    }

    // 4. Check if images are being loaded by the images API
    try {
      console.log('🔍 Testing images API...');
      
      // Make a request to our own images API to see what it returns
      const imagesResponse = await fetch(`${process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'http://localhost:3000'}/api/gallery/images`);
      
      if (imagesResponse.ok) {
        const imagesData = await imagesResponse.json();
        trace.sources.api = {
          status: 'success',
          totalImages: imagesData.images?.length || 0,
          debug: imagesData.debug,
          sampleImages: imagesData.images?.slice(0, 2).map(img => ({
            filename: img.filename,
            url: img.url,
            source: img.source
          })) || []
        };
        
        console.log(`✅ API: Returns ${trace.sources.api.totalImages} images`);
      } else {
        trace.sources.api = {
          status: 'error',
          message: `API returned ${imagesResponse.status}`
        };
      }
    } catch (apiError) {
      trace.sources.api = {
        status: 'error',
        message: apiError.message
      };
    }

    // 5. Analysis
    const kvCount = trace.sources.kv?.imageCount || 0;
    const globalCount = trace.sources.global?.imageCount || 0;
    const fsCount = Object.values(trace.sources.filesystem || {})
      .reduce((total, dir) => total + (dir.imageCount || 0), 0);
    const apiCount = trace.sources.api?.totalImages || 0;

    trace.analysis = {
      kvImages: kvCount,
      globalImages: globalCount,
      filesystemImages: fsCount,
      apiImages: apiCount,
      persistent4Images: kvCount === 4,
      likelySource: kvCount === 4 ? 'KV storage contains the 4 persistent images' : 'Unknown'
    };

    // 6. Recommendations
    if (kvCount === 4) {
      trace.recommendations.push('The 4 images are definitely in KV storage');
      trace.recommendations.push('Nuclear reset may not be working properly');
      trace.recommendations.push('Try manual KV key deletion');
    }
    
    if (fsCount > 0) {
      trace.recommendations.push(`Found ${fsCount} files in file system - these may be restored to KV`);
    }
    
    if (globalCount > 0) {
      trace.recommendations.push(`Found ${globalCount} images in global storage`);
    }

    console.log('=== TRACE IMAGES END ===');
    return NextResponse.json(trace);

  } catch (error) {
    console.error('Trace images error:', error);
    return NextResponse.json({
      error: 'Trace failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
