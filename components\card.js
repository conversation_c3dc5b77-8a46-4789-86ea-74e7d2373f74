import Image from "next/image";

export default function Card({
  flipped = false,
  service,
  description,
  src,
  alt,
}) {
  return (
    <li className="mt-10">
      <div className="grid gap-20 md:gap-6 lg:gap-10 xl:gap-20 lg:grid-cols-2">
        <div className="text-center items-center">
          <h2 className="text-transparent bg-clip-text bg-gradient-to-r from-amber-600 from-20% via-yellow-200 via-50% to-yellow-400 to-70% drop-shadow-xl text-3xl text-center font-bold">
            {service}
          </h2>

          <p className="pt-4 drop-shadow-md">{description}</p>
        </div>
        <Image
          className="hidden rounded-lg shadow-xl h-auto w-auto sm:block md:block md:justify-self-center lg:my-auto lg:block lg:justify-self-end"
          src={src}
          width={500}
          height={250}
          alt={alt}
        ></Image>
      </div>
      <hr className="border-yellow-300 mt-10"></hr>
    </li>
  );
}
