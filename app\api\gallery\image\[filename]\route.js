import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET(request, { params }) {
  try {
    const { filename } = params;
    console.log('=== IMAGE ROUTE START ===');
    console.log('Serving image:', filename);
    console.log('Timestamp:', new Date().toISOString());

    // Try to get the image from both global storage and KV
    let image = null;

    // First try global storage (dev)
    const uploadedImages = global.galleryImages || [];
    image = uploadedImages.find(img => img.filename === filename);

    if (image) {
      console.log('Found image in global storage');
    } else {
      // Try Vercel KV (production)
      try {
        if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
          console.log('Checking KV for image:', filename);
          const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
            headers: {
              'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            },
          });

          if (response.ok) {
            const kvData = await response.json();
            let kvImages = kvData.result || [];

            // Handle double-encoded JSON from KV
            if (typeof kvImages === 'string') {
              console.log('KV result is string, parsing again for image:', filename);
              try {
                kvImages = JSON.parse(kvImages);
                console.log('Successfully parsed KV string to array for image lookup');
              } catch (parseError) {
                console.log('Failed to parse KV string for image:', parseError.message);
                kvImages = [];
              }
            }

            if (Array.isArray(kvImages)) {
              image = kvImages.find(img => img && img.filename === filename);
              if (image) {
                console.log('Found image in KV storage:', filename);
              } else {
                console.log('Image not found in KV array of', kvImages.length, 'images');
              }
            } else {
              console.log('KV images is not an array:', typeof kvImages);
            }
          }
        }
      } catch (kvError) {
        console.error('KV error when serving image:', kvError);
      }
    }

    if (!image) {
      console.log('Image not found:', filename);
      console.log('Available images in global:', global.galleryImages ? global.galleryImages.map(img => img.filename) : 'none');
      return new NextResponse('Image not found', {
        status: 404,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    }

    // Validate image data
    if (!image.base64Data) {
      console.error('Image found but no base64Data:', filename);
      return new NextResponse('Image data corrupted', { status: 500 });
    }

    // Convert base64 back to buffer
    try {
      const buffer = Buffer.from(image.base64Data, 'base64');
      console.log('Serving image buffer, size:', buffer.length, 'for file:', filename);

      if (buffer.length === 0) {
        console.error('Empty buffer for image:', filename);
        return new NextResponse('Empty image data', { status: 500 });
      }

      // Return the image with proper headers
      return new NextResponse(buffer, {
        status: 200,
        headers: {
          'Content-Type': image.mimeType || image.type || 'image/jpeg',
          'Content-Length': buffer.length.toString(),
          'Cache-Control': 'public, max-age=60, must-revalidate',
          'ETag': `"${Date.now()}-${filename}"`,
          'Last-Modified': new Date().toUTCString(),
        },
      });

    } catch (bufferError) {
      console.error('Error creating buffer for image:', filename, bufferError);
      return new NextResponse('Failed to process image data', { status: 500 });
    }

  } catch (error) {
    console.error('Error serving image:', filename, error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
