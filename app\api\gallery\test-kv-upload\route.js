import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== KV UPLOAD TEST START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      steps: [],
      success: false
    };

    // Step 1: Read current KV data
    results.steps.push({ step: 1, action: 'Reading current KV data' });
    
    const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let existingImages = [];
    
    if (getResponse.ok) {
      const responseText = await getResponse.text();
      const kvData = JSON.parse(responseText);
      let kvResult = kvData.result || [];
      
      results.steps.push({ 
        step: 1, 
        status: 'success', 
        rawDataType: typeof kvResult,
        rawDataLength: typeof kvResult === 'string' ? kvResult.length : 'N/A'
      });

      // Handle double-encoded JSON
      if (typeof kvResult === 'string') {
        try {
          existingImages = JSON.parse(kvResult);
          results.steps.push({ step: 1, parseStatus: 'success', imageCount: existingImages.length });
        } catch (parseError) {
          results.steps.push({ step: 1, parseStatus: 'failed', error: parseError.message });
          existingImages = [];
        }
      } else if (Array.isArray(kvResult)) {
        existingImages = kvResult;
        results.steps.push({ step: 1, parseStatus: 'direct_array', imageCount: existingImages.length });
      }
    } else {
      results.steps.push({ step: 1, status: 'failed', httpStatus: getResponse.status });
    }

    // Step 2: Create a test image
    results.steps.push({ step: 2, action: 'Creating test image' });
    
    const testImage = {
      filename: `test_${Date.now()}_kv_upload.webp`,
      title: 'KV Upload Test Image',
      description: 'This is a test image to verify KV upload functionality',
      category: 'test',
      uploadDate: new Date().toISOString(),
      originalName: 'kv_test.webp',
      size: 1234,
      type: 'image/webp',
      base64Data: 'UklGRiQAAABXRUJQVlA4IBgAAAAwAQCdASoBAAEAAwA0JaQAA3AA/vuUAAA=', // Tiny 1x1 WebP
      mimeType: 'image/webp'
    };

    // Step 3: Add test image to existing array
    const updatedImages = [...existingImages, testImage];
    results.steps.push({ 
      step: 3, 
      action: 'Adding test image', 
      originalCount: existingImages.length,
      newCount: updatedImages.length 
    });

    // Step 4: Attempt to save to KV
    results.steps.push({ step: 4, action: 'Saving to KV' });
    
    const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updatedImages),
    });

    if (setResponse.ok) {
      results.steps.push({ step: 4, status: 'success', httpStatus: setResponse.status });
      
      // Step 5: Verify the save
      results.steps.push({ step: 5, action: 'Verifying save' });
      
      const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (verifyResponse.ok) {
        const verifyText = await verifyResponse.text();
        const verifyData = JSON.parse(verifyText);
        let verifyResult = verifyData.result || [];
        
        if (typeof verifyResult === 'string') {
          try {
            verifyResult = JSON.parse(verifyResult);
          } catch (e) {
            verifyResult = [];
          }
        }
        
        const finalCount = Array.isArray(verifyResult) ? verifyResult.length : 0;
        results.steps.push({ 
          step: 5, 
          status: 'success', 
          finalImageCount: finalCount,
          testImageFound: Array.isArray(verifyResult) && verifyResult.some(img => img.filename === testImage.filename)
        });
        
        if (finalCount === updatedImages.length) {
          results.success = true;
          results.message = 'KV upload test successful!';
        } else {
          results.message = `KV save failed - expected ${updatedImages.length} images, found ${finalCount}`;
        }
      } else {
        results.steps.push({ step: 5, status: 'failed', httpStatus: verifyResponse.status });
        results.message = 'Could not verify KV save';
      }
    } else {
      const errorText = await setResponse.text();
      results.steps.push({ 
        step: 4, 
        status: 'failed', 
        httpStatus: setResponse.status,
        error: errorText 
      });
      results.message = `KV save failed: ${setResponse.status} - ${errorText}`;
    }

    console.log('=== KV UPLOAD TEST END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('KV upload test error:', error);
    return NextResponse.json({
      error: 'KV upload test failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
