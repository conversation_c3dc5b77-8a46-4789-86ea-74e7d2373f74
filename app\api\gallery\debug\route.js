import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== PRODUCTION DEBUG API START ===');
  
  try {
    const debug = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      vercel: {
        region: process.env.VERCEL_REGION || 'unknown',
        url: process.env.VERCEL_URL || 'unknown'
      },
      kv: {
        configured: !!(process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN),
        urlExists: !!process.env.KV_REST_API_URL,
        tokenExists: !!process.env.KV_REST_API_TOKEN
      },
      global: {
        imagesCount: global.galleryImages ? global.galleryImages.length : 0,
        exists: !!global.galleryImages
      }
    };

    // Test KV connection
    if (debug.kv.configured) {
      try {
        console.log('Testing KV connection...');
        const kvResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        debug.kv.connectionTest = {
          status: kvResponse.status,
          ok: kvResponse.ok,
          statusText: kvResponse.statusText
        };

        if (kvResponse.ok) {
          const kvData = await kvResponse.json();
          let kvImages = kvData.result || [];
          
          // Handle double-encoded JSON
          if (typeof kvImages === 'string') {
            try {
              kvImages = JSON.parse(kvImages);
            } catch (e) {
              debug.kv.parseError = e.message;
            }
          }

          debug.kv.data = {
            hasResult: !!kvData.result,
            resultType: typeof kvData.result,
            imagesCount: Array.isArray(kvImages) ? kvImages.length : 0,
            sampleImage: Array.isArray(kvImages) && kvImages.length > 0 ? {
              filename: kvImages[0].filename,
              title: kvImages[0].title,
              hasBase64: !!kvImages[0].base64Data,
              base64Length: kvImages[0].base64Data ? kvImages[0].base64Data.length : 0
            } : null
          };
        } else {
          const errorText = await kvResponse.text();
          debug.kv.error = errorText;
        }
      } catch (kvError) {
        debug.kv.connectionError = kvError.message;
      }
    }

    console.log('Debug info:', debug);
    console.log('=== PRODUCTION DEBUG API END ===');

    return NextResponse.json(debug);

  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json({ 
      error: 'Debug API failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
