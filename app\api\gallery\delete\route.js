import { NextResponse } from 'next/server';
import { unlink, readFile, writeFile } from 'fs/promises';
import { existsSync } from 'fs';
import { cookies } from 'next/headers';
import path from 'path';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Function to check authentication
function checkAuth() {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('admin-session');

  if (!sessionToken) {
    return false;
  }

  try {
    const decoded = Buffer.from(sessionToken.value, 'base64').toString();
    const [user, timestamp] = decoded.split(':');

    if (user === 'admin') {
      const tokenAge = Date.now() - parseInt(timestamp);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      return tokenAge < maxAge;
    }
  } catch (error) {
    return false;
  }

  return false;
}

export async function DELETE(request) {
  // Check authentication first
  if (!checkAuth()) {
    return NextResponse.json({
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const { filename } = await request.json();

    if (!filename) {
      return NextResponse.json({ error: 'Filename is required' }, { status: 400 });
    }

    // Try to delete from both global storage and Vercel KV
    let deletedFromGlobal = false;
    let deletedFromKV = false;

    // Delete from global storage (dev)
    if (global.galleryImages) {
      const initialLength = global.galleryImages.length;
      global.galleryImages = global.galleryImages.filter(img => img.filename !== filename);
      deletedFromGlobal = global.galleryImages.length < initialLength;
      if (deletedFromGlobal) {
        console.log('Image deleted from global storage');
      }
    }

    // Delete from Vercel KV (production)
    try {
      if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
        console.log('Deleting from Vercel KV...');

        // Get existing images from KV
        const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        if (getResponse.ok) {
          const kvData = await getResponse.json();
          let existingImages = kvData.result || [];

          // Handle double-encoded JSON from KV
          if (typeof existingImages === 'string') {
            console.log('KV result is string, parsing for delete operation. Length:', existingImages.length);
            try {
              existingImages = JSON.parse(existingImages);
              console.log('Successfully parsed KV string for delete. Images found:', Array.isArray(existingImages) ? existingImages.length : 'not an array');
            } catch (parseError) {
              console.log('Failed to parse KV string for delete:', parseError.message);
              existingImages = [];
            }
          }

          // Validate that we have an array
          if (!Array.isArray(existingImages)) {
            console.log('KV data is not an array for delete:', typeof existingImages);
            console.log('KV data value:', existingImages);
            existingImages = [];
          }

          console.log('Delete operation - existing images count:', existingImages.length);
          console.log('Looking for filename to delete:', filename);

          // CORRUPTION PROTECTION DISABLED - persistent images issue resolved

          console.log('Deleting from', existingImages.length, 'images in KV');
          const filteredImages = existingImages.filter(img => img && img.filename !== filename);

          if (filteredImages.length < existingImages.length) {
            // Store updated array back to KV
            const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(filteredImages),
            });

            if (setResponse.ok) {
              deletedFromKV = true;
              console.log('Image deleted from Vercel KV');
            }
          }
        }
      }
    } catch (kvError) {
      console.error('Vercel KV delete error:', kvError);
    }

    if (deletedFromGlobal || deletedFromKV) {
      return NextResponse.json({
        success: true,
        message: 'Image deleted successfully'
      });
    }

    // Try to delete from file system (for existing images)
    try {
      const imagePath = path.join(process.cwd(), 'public/images', filename);
      if (existsSync(imagePath)) {
        await unlink(imagePath);
      }

      // Update metadata
      const metadataPath = path.join(process.cwd(), 'public/images/metadata.json');
      if (existsSync(metadataPath)) {
        const metadataContent = await readFile(metadataPath, 'utf8');
        const metadata = JSON.parse(metadataContent);

        delete metadata[filename];

        await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      }
    } catch (fsError) {
      console.log('Could not delete from file system (read-only environment):', fsError.message);
    }

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    });

  } catch (error) {
    console.error('Delete error:', error);
    return NextResponse.json({
      error: 'Failed to delete image'
    }, { status: 500 });
  }
}
