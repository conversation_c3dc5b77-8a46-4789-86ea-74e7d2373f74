import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== NUCLEAR KV RESET START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      operations: [],
      success: true,
      errors: []
    };

    // Step 1: Try to delete the main key entirely
    try {
      console.log('🔥 Step 1: Deleting gallery_images key entirely...');
      
      const deleteResponse = await fetch(`${process.env.KV_REST_API_URL}/del/gallery_images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (deleteResponse.ok) {
        results.operations.push({
          step: 1,
          operation: 'Delete KV Key',
          status: 'success',
          message: 'gallery_images key deleted entirely'
        });
        console.log('✅ Key deleted successfully');
      } else {
        const errorText = await deleteResponse.text();
        results.operations.push({
          step: 1,
          operation: 'Delete KV Key',
          status: 'warning',
          message: `Delete failed: ${deleteResponse.status} - ${errorText}`
        });
        console.log('⚠️ Key deletion failed, continuing...');
      }
    } catch (deleteError) {
      results.operations.push({
        step: 1,
        operation: 'Delete KV Key',
        status: 'error',
        message: deleteError.message
      });
      console.log('❌ Key deletion error:', deleteError.message);
    }

    // Step 2: Wait a moment for KV to process
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Step 3: Verify the key is gone
    try {
      console.log('🔍 Step 2: Verifying key deletion...');
      
      const checkResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (checkResponse.status === 404) {
        results.operations.push({
          step: 2,
          operation: 'Verify Deletion',
          status: 'success',
          message: 'Key successfully deleted (404 response)'
        });
        console.log('✅ Key confirmed deleted');
      } else if (checkResponse.ok) {
        const checkData = await checkResponse.json();
        results.operations.push({
          step: 2,
          operation: 'Verify Deletion',
          status: 'warning',
          message: `Key still exists: ${JSON.stringify(checkData)}`
        });
        console.log('⚠️ Key still exists:', checkData);
      } else {
        results.operations.push({
          step: 2,
          operation: 'Verify Deletion',
          status: 'warning',
          message: `Unexpected response: ${checkResponse.status}`
        });
      }
    } catch (checkError) {
      results.operations.push({
        step: 2,
        operation: 'Verify Deletion',
        status: 'error',
        message: checkError.message
      });
    }

    // Step 4: Create fresh empty key
    try {
      console.log('🆕 Step 3: Creating fresh empty key...');
      
      const createResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify([]), // Fresh empty array
      });

      if (createResponse.ok) {
        results.operations.push({
          step: 3,
          operation: 'Create Fresh Key',
          status: 'success',
          message: 'Fresh empty gallery_images key created'
        });
        console.log('✅ Fresh key created');
      } else {
        const errorText = await createResponse.text();
        results.operations.push({
          step: 3,
          operation: 'Create Fresh Key',
          status: 'error',
          message: `Creation failed: ${createResponse.status} - ${errorText}`
        });
        results.errors.push('Failed to create fresh key');
        results.success = false;
      }
    } catch (createError) {
      results.operations.push({
        step: 3,
        operation: 'Create Fresh Key',
        status: 'error',
        message: createError.message
      });
      results.errors.push('Fresh key creation failed');
      results.success = false;
    }

    // Step 5: Final verification
    try {
      console.log('✅ Step 4: Final verification...');
      
      const finalResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (finalResponse.ok) {
        const finalData = await finalResponse.json();
        let finalImages = finalData.result || [];
        
        if (typeof finalImages === 'string') {
          try {
            finalImages = JSON.parse(finalImages);
          } catch (e) {
            finalImages = [];
          }
        }
        
        const imageCount = Array.isArray(finalImages) ? finalImages.length : 'unknown';
        results.operations.push({
          step: 4,
          operation: 'Final Verification',
          status: 'success',
          message: `KV now contains ${imageCount} images`,
          imageCount: imageCount
        });
        console.log(`✅ Final verification: ${imageCount} images`);
      } else {
        results.operations.push({
          step: 4,
          operation: 'Final Verification',
          status: 'error',
          message: `Verification failed: ${finalResponse.status}`
        });
      }
    } catch (finalError) {
      results.operations.push({
        step: 4,
        operation: 'Final Verification',
        status: 'error',
        message: finalError.message
      });
    }

    // Step 6: Clear file system images (THE REAL CULPRIT!)
    try {
      console.log('🗂️ Step 5: Clearing file system images...');

      const fs = require('fs').promises;
      const path = require('path');

      const directories = [
        path.join(process.cwd(), 'public', 'images'),
        path.join(process.cwd(), 'public', 'uploads'),
        path.join(process.cwd(), 'public', 'gallery')
      ];

      let totalFilesDeleted = 0;

      for (const dir of directories) {
        try {
          console.log(`Checking directory: ${dir}`);
          const files = await fs.readdir(dir);
          const imageFiles = files.filter(file =>
            /\.(jpg|jpeg|png|gif|webp)$/i.test(file)
          );

          console.log(`Found ${imageFiles.length} image files in ${dir}`);

          for (const file of imageFiles) {
            const filePath = path.join(dir, file);
            await fs.unlink(filePath);
            console.log(`Deleted: ${filePath}`);
            totalFilesDeleted++;
          }

          // Also delete metadata.json if it exists
          const metadataPath = path.join(dir, 'metadata.json');
          try {
            await fs.unlink(metadataPath);
            console.log(`Deleted metadata: ${metadataPath}`);
          } catch (metaError) {
            // Metadata file might not exist, that's okay
          }

        } catch (dirError) {
          console.log(`Directory ${dir} not accessible:`, dirError.message);
        }
      }

      results.operations.push({
        step: 5,
        operation: 'Clear File System Images',
        status: 'success',
        message: `Deleted ${totalFilesDeleted} image files from file system`,
        filesDeleted: totalFilesDeleted
      });
      console.log(`✅ File system cleanup: ${totalFilesDeleted} files deleted`);

    } catch (fsError) {
      results.operations.push({
        step: 5,
        operation: 'Clear File System Images',
        status: 'warning',
        message: `File system cleanup limited in serverless: ${fsError.message}`
      });
      console.log('⚠️ File system cleanup limited:', fsError.message);
    }

    // Step 7: Clear global storage too
    global.galleryImages = [];
    results.operations.push({
      step: 6,
      operation: 'Clear Global Storage',
      status: 'success',
      message: 'Global storage cleared'
    });

    console.log('=== SUPER NUCLEAR RESET END ===');
    console.log('Results:', results);

    return NextResponse.json(results);

  } catch (error) {
    console.error('Nuclear reset error:', error);
    return NextResponse.json({
      error: 'Nuclear reset failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
