import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST() {
  console.log('=== GALLERY BACKUP SYSTEM ===');
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    const timestamp = new Date().toISOString();
    const backupKey = `gallery_backup_${timestamp.split('T')[0]}`; // Daily backup
    
    // Get current gallery images
    console.log('Fetching current gallery images...');
    const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (!response.ok) {
      return NextResponse.json({
        error: 'Failed to fetch current images',
        status: response.status
      }, { status: 500 });
    }

    const rawText = await response.text();
    let kvData;
    
    try {
      kvData = JSON.parse(rawText);
    } catch (parseError) {
      return NextResponse.json({
        error: 'Failed to parse KV response',
        details: parseError.message
      }, { status: 500 });
    }

    let images = kvData.result || [];
    
    // Handle double-encoded data
    if (typeof images === 'string') {
      try {
        images = JSON.parse(images);
      } catch (e) {
        console.log('Failed to parse double-encoded data:', e.message);
      }
    }

    // Ensure it's an array
    if (!Array.isArray(images)) {
      images = [];
    }

    // Create backup with metadata
    const backupData = {
      timestamp,
      imageCount: images.length,
      images: images,
      metadata: {
        backupVersion: '1.0',
        source: 'gallery_images',
        environment: process.env.NODE_ENV || 'unknown'
      }
    };

    // Save backup
    console.log(`Creating backup with key: ${backupKey}`);
    const backupResponse = await fetch(`${process.env.KV_REST_API_URL}/set/${backupKey}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(backupData),
    });

    if (!backupResponse.ok) {
      const errorText = await backupResponse.text();
      return NextResponse.json({
        error: 'Failed to create backup',
        status: backupResponse.status,
        details: errorText
      }, { status: 500 });
    }

    // Also create a "latest backup" for quick recovery
    const latestBackupResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_backup_latest`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(backupData),
    });

    console.log('Backup created successfully');

    return NextResponse.json({
      success: true,
      message: 'Backup created successfully',
      backupKey,
      imageCount: images.length,
      timestamp,
      backupSize: JSON.stringify(backupData).length
    });

  } catch (error) {
    console.error('Backup failed:', error);
    return NextResponse.json({
      error: 'Backup operation failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET endpoint to list available backups
export async function GET() {
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    // Check for recent backups (last 7 days)
    const backups = [];
    const today = new Date();
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      const backupKey = `gallery_backup_${dateStr}`;
      
      try {
        const response = await fetch(`${process.env.KV_REST_API_URL}/get/${backupKey}`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });
        
        if (response.ok) {
          const backupText = await response.text();
          const backupData = JSON.parse(backupText);
          const backup = JSON.parse(backupData.result);
          
          backups.push({
            key: backupKey,
            date: dateStr,
            imageCount: backup.imageCount,
            timestamp: backup.timestamp,
            size: backupText.length
          });
        }
      } catch (e) {
        // Backup doesn't exist for this date, continue
      }
    }

    return NextResponse.json({
      success: true,
      availableBackups: backups,
      totalBackups: backups.length
    });

  } catch (error) {
    console.error('Failed to list backups:', error);
    return NextResponse.json({
      error: 'Failed to list backups',
      details: error.message
    }, { status: 500 });
  }
}
