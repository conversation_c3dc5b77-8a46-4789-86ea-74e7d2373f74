import { Inter } from "next/font/google";
import "./globals.css";
import { Flowbite} from "flowbite-react";
import DefaultNavbar from "../components/navbar";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Express Demos & Renos",
  description: "Express Demos & Renos Ottawa Website",
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}) {
  return (
    <html className="dark h-full w-100" lang="en">
      <body className={inter.className}>
        <Flowbite>
          <DefaultNavbar />
          {children}
        </Flowbite>
      </body>
    </html>
  );
}
