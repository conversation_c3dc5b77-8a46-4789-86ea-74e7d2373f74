import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Function to check authentication
function checkAuth() {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('admin-session');

  if (!sessionToken) {
    return false;
  }

  try {
    const decoded = Buffer.from(sessionToken.value, 'base64').toString();
    const [user, timestamp] = decoded.split(':');

    if (user === 'admin') {
      const tokenAge = Date.now() - parseInt(timestamp);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      return tokenAge < maxAge;
    }
  } catch (error) {
    return false;
  }

  return false;
}

// Generate Cloudinary signature for secure upload
function generateSignature(params, apiSecret) {
  const crypto = require('crypto');

  // Sort parameters alphabetically
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');

  // Create signature
  const signature = crypto
    .createHash('sha1')
    .update(sortedParams + apiSecret)
    .digest('hex');

  return signature;
}

// Upload to Cloudinary (for production) - Server-side signed upload
async function uploadToCloudinary(file, metadata) {
  const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
  const apiKey = process.env.CLOUDINARY_API_KEY;
  const apiSecret = process.env.CLOUDINARY_API_SECRET;

  if (!cloudName || !apiKey || !apiSecret) {
    throw new Error('Cloudinary credentials not configured');
  }

  // Convert file to base64
  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);
  const base64Data = `data:${file.type};base64,${buffer.toString('base64')}`;

  // Create unique public_id
  const timestamp = Math.round(Date.now() / 1000);
  const publicId = `gallery/${timestamp}_${metadata.title.replace(/[^a-zA-Z0-9]/g, '_')}`;

  // Prepare parameters for signature
  const uploadParams = {
    timestamp: timestamp,
    public_id: publicId,
    folder: 'express_renos_gallery',
    tags: `category:${metadata.category},uploaded:${new Date().toISOString().split('T')[0]}`,
    context: `title=${encodeURIComponent(metadata.title)}|description=${encodeURIComponent(metadata.description)}`,
  };

  // Generate signature
  const signature = generateSignature(uploadParams, apiSecret);

  // Upload to Cloudinary using signed upload (server-side)
  const formData = new FormData();
  formData.append('file', base64Data);
  formData.append('api_key', apiKey);
  formData.append('timestamp', uploadParams.timestamp.toString());
  formData.append('public_id', uploadParams.public_id);
  formData.append('folder', uploadParams.folder);
  formData.append('tags', uploadParams.tags);
  formData.append('context', uploadParams.context);
  formData.append('signature', signature);

  const uploadResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!uploadResponse.ok) {
    const errorText = await uploadResponse.text();
    throw new Error(`Cloudinary upload failed: ${uploadResponse.status} - ${errorText}`);
  }

  const uploadResult = await uploadResponse.json();

  return {
    publicId: uploadResult.public_id,
    url: uploadResult.secure_url,
    width: uploadResult.width,
    height: uploadResult.height,
    format: uploadResult.format,
    bytes: uploadResult.bytes,
    createdAt: uploadResult.created_at
  };
}

// Simple storage using environment variables or global state
let imageStorage = global.imageStorage || [];

function saveToMemoryStorage(imageData) {
  if (!global.imageStorage) {
    global.imageStorage = [];
  }
  
  global.imageStorage.push(imageData);
  imageStorage = global.imageStorage;
  
  return imageData;
}

function getFromMemoryStorage() {
  return global.imageStorage || [];
}

export async function POST(request) {
  console.log('=== SMART UPLOAD START ===');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Vercel:', !!process.env.VERCEL);

  try {
    // Check authentication
    if (!checkAuth()) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file');
    const title = formData.get('title') || 'Untitled';
    const description = formData.get('description') || '';
    const category = formData.get('category') || 'general';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
      }, { status: 400 });
    }

    // Validate file size
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({
        error: 'File too large. Maximum size is 10MB.'
      }, { status: 400 });
    }

    const timestamp = Date.now();
    const filename = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;

    // Determine storage method
    const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL;
    const hasCloudinary = process.env.CLOUDINARY_CLOUD_NAME && process.env.CLOUDINARY_API_KEY && process.env.CLOUDINARY_API_SECRET;

    console.log('Environment check:', {
      NODE_ENV: process.env.NODE_ENV,
      VERCEL: !!process.env.VERCEL,
      isProduction,
      hasCloudinary,
      cloudName: process.env.CLOUDINARY_CLOUD_NAME ? 'Set' : 'Missing'
    });

    let imageData;
    let storageMethod;

    if (isProduction && hasCloudinary) {
      // Use Cloudinary for production
      console.log('Using Cloudinary storage for production');

      try {
        const cloudinaryResult = await uploadToCloudinary(file, { title, description, category });

        imageData = {
          filename,
          title,
          description,
          category,
          uploadDate: new Date().toISOString(),
          originalName: file.name,
          size: file.size,
          type: file.type,
          url: cloudinaryResult.url, // Direct Cloudinary URL
          cloudinary: cloudinaryResult,
          source: 'cloudinary'
        };

        storageMethod = 'cloudinary';
        console.log('✅ Cloudinary upload successful, URL:', cloudinaryResult.url);
      } catch (cloudinaryError) {
        console.error('Cloudinary upload failed:', cloudinaryError);
        // Fall back to memory storage
        storageMethod = 'memory_fallback';
      }
    }

    if (!imageData) {
      // Use memory storage (for development or fallback)
      console.log('Using memory storage');
      
      // Convert file to base64 for storage
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      const base64Data = buffer.toString('base64');
      
      imageData = {
        filename,
        title,
        description,
        category,
        uploadDate: new Date().toISOString(),
        originalName: file.name,
        size: file.size,
        type: file.type,
        base64Data,
        url: `/api/gallery/smart-upload/image/${filename}`, // We'll serve this via API
        source: 'memory'
      };
      
      storageMethod = 'memory';
    }

    // Save to storage
    saveToMemoryStorage(imageData);

    console.log(`✅ Image uploaded successfully using ${storageMethod} storage`);

    return NextResponse.json({
      success: true,
      message: `Image uploaded successfully using ${storageMethod} storage`,
      image: {
        filename: imageData.filename,
        title: imageData.title,
        category: imageData.category,
        url: imageData.url,
        uploadDate: imageData.uploadDate
      },
      storageMethod
    });

  } catch (error) {
    console.error('Smart upload failed:', error);
    return NextResponse.json({
      error: 'Upload failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// DELETE endpoint to remove images
export async function DELETE(request) {
  console.log('=== SMART DELETE START ===');

  try {
    // Check authentication
    if (!checkAuth()) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const filename = searchParams.get('filename');

    if (!filename) {
      return NextResponse.json({ error: 'Filename required' }, { status: 400 });
    }

    console.log('Attempting to delete:', filename);

    // Get current images
    const images = getFromMemoryStorage();
    const imageToDelete = images.find(img => img.filename === filename);

    if (!imageToDelete) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    // Determine if we need to delete from Cloudinary
    const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL;
    const hasCloudinary = process.env.CLOUDINARY_CLOUD_NAME && process.env.CLOUDINARY_API_KEY && process.env.CLOUDINARY_API_SECRET;

    if (isProduction && hasCloudinary && imageToDelete.cloudinary?.publicId) {
      // Delete from Cloudinary
      console.log('Deleting from Cloudinary:', imageToDelete.cloudinary.publicId);

      try {
        const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
        const apiKey = process.env.CLOUDINARY_API_KEY;
        const apiSecret = process.env.CLOUDINARY_API_SECRET;

        const publicId = imageToDelete.cloudinary.publicId;

        // Use the Admin API with proper authentication
        const deleteResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/destroy`, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            public_id: publicId,
          }),
        });

        if (deleteResponse.ok) {
          const deleteResult = await deleteResponse.json();
          console.log('✅ Cloudinary deletion successful:', deleteResult);
        } else {
          const errorText = await deleteResponse.text();
          console.log('⚠️ Cloudinary deletion failed:', deleteResponse.status, errorText);
          // Continue with local deletion even if Cloudinary fails
        }
      } catch (cloudinaryError) {
        console.error('❌ Cloudinary deletion error:', cloudinaryError);
        // Continue with local deletion even if Cloudinary fails
      }
    }

    // Remove from memory storage
    const updatedImages = images.filter(img => img.filename !== filename);
    global.imageStorage = updatedImages;

    console.log(`✅ Image deleted successfully: ${filename}`);

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully',
      deletedImage: {
        filename: imageToDelete.filename,
        title: imageToDelete.title
      }
    });

  } catch (error) {
    console.error('Delete failed:', error);
    return NextResponse.json({
      error: 'Delete failed',
      details: error.message
    }, { status: 500 });
  }
}

// GET endpoint to retrieve images
export async function GET() {
  try {
    const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL;
    const hasCloudinary = process.env.CLOUDINARY_CLOUD_NAME && process.env.CLOUDINARY_API_KEY && process.env.CLOUDINARY_API_SECRET;

    let images = [];
    let storageMethod = 'memory';

    if (isProduction && hasCloudinary) {
      // In production, get images from Cloudinary
      try {
        const cloudinaryResponse = await fetch(`${process.env.VERCEL_URL || 'https://expressrenos.com'}/api/gallery/cloudinary-metadata`);
        if (cloudinaryResponse.ok) {
          const cloudinaryData = await cloudinaryResponse.json();
          images = cloudinaryData.images || [];
          storageMethod = 'cloudinary';
        } else {
          console.log('Cloudinary fetch failed, falling back to memory');
          images = getFromMemoryStorage();
        }
      } catch (cloudinaryError) {
        console.error('Cloudinary fetch error:', cloudinaryError);
        images = getFromMemoryStorage();
      }
    } else {
      // In development, use memory storage
      images = getFromMemoryStorage();
    }

    // Convert to display format
    const displayImages = images.map(img => ({
      filename: img.filename,
      title: img.title,
      description: img.description,
      category: img.category,
      url: img.url,
      uploadDate: img.uploadDate,
      source: img.source
    }));

    // Sort by upload date (newest first)
    displayImages.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));

    return NextResponse.json({
      success: true,
      images: displayImages,
      totalCount: displayImages.length,
      storageMethod
    });

  } catch (error) {
    console.error('Failed to retrieve images:', error);
    return NextResponse.json({
      error: 'Failed to retrieve images',
      details: error.message
    }, { status: 500 });
  }
}
