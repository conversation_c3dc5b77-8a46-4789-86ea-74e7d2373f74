import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== SIMPLE IMAGES API START ===');

  try {
    // Check KV storage directly
    let kvImages = [];
    let kvError = null;

    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      try {
        const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          },
        });

        if (response.ok) {
          const kvData = await response.json();
          kvImages = kvData.result || [];

          // Handle double-encoded JSON
          if (typeof kvImages === 'string') {
            kvImages = JSON.parse(kvImages);
          }
        } else {
          kvError = `KV response: ${response.status} ${response.statusText}`;
        }
      } catch (error) {
        kvError = error.message;
      }
    }

    const response = {
      message: 'Simple images API working',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      globalImagesCount: global.galleryImages ? global.galleryImages.length : 0,
      kvConfigured: !!(process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN),
      kvImagesCount: Array.isArray(kvImages) ? kvImages.length : 0,
      kvError: kvError,
      kvSample: Array.isArray(kvImages) && kvImages.length > 0 ? {
        filename: kvImages[0].filename,
        title: kvImages[0].title,
        hasBase64: !!kvImages[0].base64Data
      } : null
    };

    console.log('Simple API response:', response);
    console.log('=== SIMPLE IMAGES API END ===');

    return NextResponse.json(response);

  } catch (error) {
    console.error('Simple API error:', error);
    return NextResponse.json({
      error: 'Simple API failed',
      details: error.message
    }, { status: 500 });
  }
}
