import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const filename = searchParams.get('filename') || '1751930780540_Captureb.PNG';

    const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    const debug = {
      timestamp: new Date().toISOString(),
      filename: filename,
      cloudinary: {
        cloudName: cloudName ? `Set (${cloudName})` : 'Missing',
        apiKey: apiKey ? `Set (${apiKey.substring(0, 6)}...)` : 'Missing',
        apiSecret: apiSecret ? `Set (${apiSecret.substring(0, 6)}...)` : 'Missing',
        authHeader: apiKey && apiSecret ? `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64').substring(0, 20)}...` : 'Cannot create'
      }
    };

    if (!cloudName || !apiKey || !apiSecret) {
      return NextResponse.json({
        ...debug,
        error: 'Missing Cloudinary credentials'
      }, { status: 400 });
    }

    // Step 0: Test basic Cloudinary authentication
    try {
      const authTestResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/resources/image?type=upload&max_results=1`, {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`
        }
      });

      debug.authTest = {
        status: authTestResponse.status,
        ok: authTestResponse.ok,
        statusText: authTestResponse.statusText
      };

      if (!authTestResponse.ok) {
        const errorText = await authTestResponse.text();
        debug.authTest.error = errorText;
      }
    } catch (authError) {
      debug.authTest = {
        error: authError.message
      };
    }

    // Step 1: Try to find the image in Cloudinary
    try {
      const listResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/resources/image?type=upload&prefix=gallery&max_results=500`, {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`
        }
      });

      if (listResponse.ok) {
        const listData = await listResponse.json();
        const allImages = listData.resources.map(r => ({
          public_id: r.public_id,
          filename: r.public_id.replace('gallery/', ''),
          url: r.secure_url
        }));

        const targetImage = listData.resources.find(resource => {
          const resourceFilename = resource.public_id.replace('gallery/', '');
          return resourceFilename === filename || resourceFilename.startsWith(filename);
        });

        debug.cloudinaryList = {
          totalImages: allImages.length,
          allImages: allImages,
          targetImage: targetImage ? {
            public_id: targetImage.public_id,
            filename: targetImage.public_id.replace('gallery/', ''),
            url: targetImage.secure_url
          } : null
        };

        // Step 2: Try to delete the image if found (using signed request)
        if (targetImage) {
          try {
            const timestamp = Math.round(Date.now() / 1000);
            const crypto = require('crypto');

            // Create signature for delete operation
            const paramsToSign = `public_id=${targetImage.public_id}&timestamp=${timestamp}${apiSecret}`;
            const signature = crypto.createHash('sha1').update(paramsToSign).digest('hex');

            const deleteResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/destroy`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: new URLSearchParams({
                public_id: targetImage.public_id,
                api_key: apiKey,
                timestamp: timestamp.toString(),
                signature: signature,
              }),
            });

            const deleteText = await deleteResponse.text();
            debug.deleteTest = {
              status: deleteResponse.status,
              ok: deleteResponse.ok,
              response: deleteText,
              publicIdUsed: targetImage.public_id,
              signatureUsed: signature,
              timestampUsed: timestamp
            };

            if (deleteResponse.ok) {
              debug.deleteTest.parsed = JSON.parse(deleteText);
            }

          } catch (deleteError) {
            debug.deleteTest = {
              error: deleteError.message,
              publicIdUsed: targetImage.public_id
            };
          }
        } else {
          debug.deleteTest = {
            error: 'Image not found in Cloudinary',
            searchedFor: filename
          };
        }

      } else {
        const errorText = await listResponse.text();
        debug.cloudinaryList = {
          error: `List failed: ${listResponse.status}`,
          details: errorText
        };
      }

    } catch (listError) {
      debug.cloudinaryList = {
        error: listError.message
      };
    }

    return NextResponse.json(debug);

  } catch (error) {
    return NextResponse.json({
      error: 'Debug failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
