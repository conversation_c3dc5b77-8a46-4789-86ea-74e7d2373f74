import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const debug = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      isVercel: !!process.env.VERCEL,
      cloudinary: {
        cloudName: process.env.CLOUDINARY_CLOUD_NAME ? 'Set' : 'Missing',
        apiKey: process.env.CLOUDINARY_API_KEY ? 'Set' : 'Missing',
        apiSecret: process.env.CLOUDINARY_API_SECRET ? 'Set' : 'Missing'
      },
      globalStorage: {
        exists: !!global.galleryImages,
        count: global.galleryImages ? global.galleryImages.length : 0,
        sample: global.galleryImages ? global.galleryImages.slice(0, 2).map(img => ({
          filename: img.filename,
          title: img.title,
          url: img.url,
          source: img.source
        })) : []
      }
    };

    return NextResponse.json(debug);

  } catch (error) {
    return NextResponse.json({
      error: 'Debug failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST() {
  try {
    // Test Cloudinary connection
    const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    if (!cloudName || !apiKey || !apiSecret) {
      return NextResponse.json({
        error: 'Cloudinary credentials missing',
        cloudName: !!cloudName,
        apiKey: !!apiKey,
        apiSecret: !!apiSecret
      }, { status: 400 });
    }

    // Test a simple Cloudinary API call
    const testResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/resources/image?max_results=1`, {
      headers: {
        'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`
      }
    });

    const testResult = {
      cloudinaryTest: {
        status: testResponse.status,
        ok: testResponse.ok
      }
    };

    if (testResponse.ok) {
      const data = await testResponse.json();
      testResult.cloudinaryTest.resourceCount = data.resources?.length || 0;
    } else {
      const errorText = await testResponse.text();
      testResult.cloudinaryTest.error = errorText;
    }

    return NextResponse.json(testResult);

  } catch (error) {
    return NextResponse.json({
      error: 'Test failed',
      details: error.message
    }, { status: 500 });
  }
}
