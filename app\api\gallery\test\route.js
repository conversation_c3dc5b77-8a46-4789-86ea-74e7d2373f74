import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== GALLERY TEST ENDPOINT ===');

  let kvImages = [];
  let kvError = null;
  let kvDetails = {};

  // Test Vercel KV connection
  try {
    console.log('Environment check:');
    console.log('- NODE_ENV:', process.env.NODE_ENV);
    console.log('- KV_REST_API_URL exists:', !!process.env.KV_REST_API_URL);
    console.log('- KV_REST_API_TOKEN exists:', !!process.env.KV_REST_API_TOKEN);

    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      console.log('Testing KV connection...');
      console.log('KV URL:', process.env.KV_REST_API_URL);

      const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      console.log('KV response status:', response.status);
      console.log('KV response headers:', Object.fromEntries(response.headers.entries()));

      const responseText = await response.text();
      console.log('KV raw response:', responseText);

      if (response.ok) {
        try {
          const kvData = JSON.parse(responseText);
          kvImages = kvData.result || [];
          console.log('KV images found:', kvImages.length);
          kvDetails = {
            status: 'success',
            responseStatus: response.status,
            dataType: typeof kvData.result,
            hasResult: !!kvData.result
          };
        } catch (parseError) {
          kvError = `Failed to parse KV response: ${parseError.message}`;
          kvDetails = { parseError: parseError.message, rawResponse: responseText };
        }
      } else {
        kvError = `KV response error: ${response.status} ${response.statusText}`;
        kvDetails = {
          status: response.status,
          statusText: response.statusText,
          rawResponse: responseText
        };
      }
    } else {
      kvError = 'KV environment variables not set';
      kvDetails = {
        hasUrl: !!process.env.KV_REST_API_URL,
        hasToken: !!process.env.KV_REST_API_TOKEN,
        urlPreview: process.env.KV_REST_API_URL ? process.env.KV_REST_API_URL.substring(0, 30) + '...' : 'Not set'
      };
    }
  } catch (error) {
    console.error('KV connection error:', error);
    kvError = error.message;
    kvDetails = { errorStack: error.stack };
  }

  const response = {
    message: 'Gallery API test endpoint',
    status: kvError ? 'error' : 'success',
    environment: process.env.NODE_ENV,
    kvConfigured: !!(process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN),
    kvUrl: process.env.KV_REST_API_URL ? 'Set' : 'Not set',
    kvToken: process.env.KV_REST_API_TOKEN ? 'Set' : 'Not set',
    globalImagesCount: global.galleryImages ? global.galleryImages.length : 0,
    kvImagesCount: kvImages.length,
    kvError: kvError,
    kvDetails: kvDetails,
    kvImages: kvImages.map(img => ({
      filename: img?.filename,
      title: img?.title,
      size: img?.size,
      uploadDate: img?.uploadDate
    })),
    timestamp: new Date().toISOString()
  };

  console.log('Returning test response:', response);
  return NextResponse.json(response);
}

export async function POST() {
  // Add a test image to verify storage works
  if (!global.galleryImages) {
    global.galleryImages = [];
  }

  const testImage = {
    filename: `test_${Date.now()}.jpg`,
    title: 'Test Image',
    description: 'This is a test image',
    category: 'general',
    uploadDate: new Date().toISOString(),
    originalName: 'test.jpg',
    size: 1024,
    type: 'image/jpeg',
    dataUrl: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A'
  };

  global.galleryImages.push(testImage);

  return NextResponse.json({
    message: 'Test image added',
    totalImages: global.galleryImages.length,
    addedImage: testImage
  });
}
