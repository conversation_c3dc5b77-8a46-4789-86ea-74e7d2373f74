import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

function validateImageData(images) {
  const issues = [];
  const fixes = [];
  
  // Check if it's an array
  if (!Array.isArray(images)) {
    issues.push('Data is not an array');
    return { isValid: false, issues, fixes, data: images };
  }
  
  // Check each image
  const validImages = [];
  images.forEach((img, index) => {
    if (!img || typeof img !== 'object') {
      issues.push(`Image ${index}: Not an object`);
      return;
    }
    
    const requiredFields = ['filename', 'title', 'category'];
    const missingFields = requiredFields.filter(field => !img[field]);
    
    if (missingFields.length > 0) {
      issues.push(`Image ${index}: Missing fields: ${missingFields.join(', ')}`);
      
      // Auto-fix missing fields
      const fixedImg = { ...img };
      if (!fixedImg.filename) fixedImg.filename = `unknown_${index}_${Date.now()}`;
      if (!fixedImg.title) fixedImg.title = 'Untitled';
      if (!fixedImg.category) fixedImg.category = 'general';
      if (!fixedImg.uploadDate) fixedImg.uploadDate = new Date().toISOString();
      
      fixes.push(`Image ${index}: Auto-fixed missing fields`);
      validImages.push(fixedImg);
    } else {
      validImages.push(img);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues,
    fixes,
    data: validImages,
    originalCount: images.length,
    validCount: validImages.length
  };
}

export async function POST() {
  console.log('=== GALLERY DATA VALIDATION & FIX ===');
  
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    // Step 1: Get current data
    console.log('Fetching current gallery data...');
    const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (!response.ok) {
      return NextResponse.json({
        error: 'Failed to fetch current data',
        status: response.status
      }, { status: 500 });
    }

    const rawText = await response.text();
    let kvData;
    
    try {
      kvData = JSON.parse(rawText);
    } catch (parseError) {
      return NextResponse.json({
        error: 'Failed to parse KV response',
        details: parseError.message
      }, { status: 500 });
    }

    let images = kvData.result;
    const originalType = typeof images;
    const originalIsArray = Array.isArray(images);
    
    console.log('Original data type:', originalType);
    console.log('Original is array:', originalIsArray);

    // Step 2: Handle double-encoding
    if (typeof images === 'string') {
      console.log('Detected string data, attempting to parse...');
      try {
        images = JSON.parse(images);
        console.log('Successfully parsed string data');
      } catch (e) {
        console.log('Failed to parse string data:', e.message);
        images = [];
      }
    }

    // Step 3: Validate and fix data
    const validation = validateImageData(images);
    console.log('Validation result:', validation.isValid);
    console.log('Issues found:', validation.issues.length);
    console.log('Fixes applied:', validation.fixes.length);

    // Step 4: Save corrected data if needed
    let saveResult = null;
    if (!validation.isValid || originalType === 'string') {
      console.log('Saving corrected data...');
      
      const saveResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validation.data),
      });

      if (saveResponse.ok) {
        saveResult = 'Data corrected and saved successfully';
        console.log('Data correction saved');
      } else {
        saveResult = `Failed to save corrections: ${saveResponse.status}`;
      }
    }

    // Step 5: Create backup after validation
    if (validation.data.length > 0) {
      console.log('Creating post-validation backup...');
      try {
        const backupResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_backup_post_validation`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            timestamp: new Date().toISOString(),
            imageCount: validation.data.length,
            images: validation.data,
            metadata: {
              validationRun: true,
              issuesFound: validation.issues.length,
              fixesApplied: validation.fixes.length
            }
          }),
        });
        
        if (backupResponse.ok) {
          console.log('Post-validation backup created');
        }
      } catch (backupError) {
        console.log('Failed to create post-validation backup:', backupError.message);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Data validation and correction completed',
      originalDataType: originalType,
      originalIsArray: originalIsArray,
      validation: {
        isValid: validation.isValid,
        issues: validation.issues,
        fixes: validation.fixes,
        originalCount: validation.originalCount,
        validCount: validation.validCount
      },
      saveResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Validation failed:', error);
    return NextResponse.json({
      error: 'Validation operation failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET endpoint to check data health
export async function GET() {
  try {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured' 
      }, { status: 400 });
    }

    const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (!response.ok) {
      return NextResponse.json({
        healthy: false,
        error: 'Failed to fetch data',
        status: response.status
      });
    }

    const rawText = await response.text();
    const kvData = JSON.parse(rawText);
    let images = kvData.result;

    if (typeof images === 'string') {
      try {
        images = JSON.parse(images);
      } catch (e) {
        return NextResponse.json({
          healthy: false,
          error: 'Double-encoded data detected',
          dataType: 'string',
          canParse: false
        });
      }
    }

    const validation = validateImageData(images);

    return NextResponse.json({
      healthy: validation.isValid && Array.isArray(images),
      dataType: typeof kvData.result,
      isArray: Array.isArray(images),
      imageCount: Array.isArray(images) ? images.length : 0,
      issues: validation.issues,
      lastChecked: new Date().toISOString()
    });

  } catch (error) {
    return NextResponse.json({
      healthy: false,
      error: 'Health check failed',
      details: error.message
    }, { status: 500 });
  }
}
