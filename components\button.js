import React from "react";

export default function Button(props) {
  const scroll = () => {
    props.refDiv.current.scrollIntoView({ behavior: 'smooth', block: 'end' })
  }
  return (
    <button onClick={scroll} className="mt-6 relative inline-flex items-center justify-center p-0.5 mb-2 me-2 overflow-hidden text-sm font-medium rounded-lg group bg-gradient-to-br from-orange-400 via-yellow-200 to-gray-200 group-hover:from-red-200 group-hover:via-red-300 group-hover:to-yellow-200 text-white hover:text-gray-900 focus:ring-2 focus:outline-none focus:ring-gray-200 focus:ring-white">
      <span className="relative px-5 py-2.5 transition-all ease-in duration-75 bg-black rounded-md group-hover:bg-opacity-0">
        {props.buttonText}
      </span>
    </button>
  );
}
