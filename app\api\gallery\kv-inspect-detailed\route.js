import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== DETAILED KV INSPECTION START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      rawKvResponse: null,
      parsedData: null,
      imageAnalysis: [],
      parsingSteps: [],
      finalImageCount: 0,
      error: null
    };

    try {
      console.log('🔍 Step 1: Raw KV fetch...');
      
      const response = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (!response.ok) {
        results.error = `KV fetch failed: ${response.status}`;
        return NextResponse.json(results);
      }

      const responseText = await response.text();
      results.rawKvResponse = {
        length: responseText.length,
        preview: responseText.substring(0, 500),
        full: responseText.length < 2000 ? responseText : 'Too large to display'
      };

      results.parsingSteps.push({
        step: 1,
        operation: 'Raw KV Fetch',
        status: 'success',
        message: `Got ${responseText.length} characters`
      });

      console.log('🔍 Step 2: Parse JSON...');
      
      let kvData;
      try {
        kvData = JSON.parse(responseText);
        results.parsingSteps.push({
          step: 2,
          operation: 'Parse JSON',
          status: 'success',
          message: 'JSON parsed successfully',
          keys: Object.keys(kvData)
        });
      } catch (parseError) {
        results.error = `JSON parse failed: ${parseError.message}`;
        results.parsingSteps.push({
          step: 2,
          operation: 'Parse JSON',
          status: 'error',
          message: parseError.message
        });
        return NextResponse.json(results);
      }

      console.log('🔍 Step 3: Extract result...');
      
      let kvImages = kvData.result || [];
      results.parsingSteps.push({
        step: 3,
        operation: 'Extract Result',
        status: 'success',
        message: `Extracted result, type: ${typeof kvImages}`,
        resultType: typeof kvImages,
        isArray: Array.isArray(kvImages),
        length: Array.isArray(kvImages) ? kvImages.length : 'N/A'
      });

      console.log('🔍 Step 4: Handle double-encoding...');
      
      if (typeof kvImages === 'string') {
        console.log('Result is string, attempting to parse...');
        try {
          const originalLength = kvImages.length;
          kvImages = JSON.parse(kvImages);
          results.parsingSteps.push({
            step: 4,
            operation: 'Parse String Result',
            status: 'success',
            message: `Parsed string (${originalLength} chars) to ${typeof kvImages}`,
            originalLength,
            newType: typeof kvImages,
            newIsArray: Array.isArray(kvImages),
            newLength: Array.isArray(kvImages) ? kvImages.length : 'N/A'
          });
        } catch (stringParseError) {
          results.parsingSteps.push({
            step: 4,
            operation: 'Parse String Result',
            status: 'error',
            message: stringParseError.message
          });
          kvImages = [];
        }
      } else {
        results.parsingSteps.push({
          step: 4,
          operation: 'Handle Double-Encoding',
          status: 'skipped',
          message: 'Result was not a string, no double-encoding'
        });
      }

      console.log('🔍 Step 5: Analyze images...');
      
      if (Array.isArray(kvImages)) {
        results.finalImageCount = kvImages.length;
        
        kvImages.forEach((img, index) => {
          const analysis = {
            index,
            hasFilename: !!(img && img.filename),
            hasTitle: !!(img && img.title),
            hasBase64: !!(img && img.base64Data),
            hasUploadDate: !!(img && img.uploadDate),
            filename: img?.filename || 'missing',
            title: img?.title || 'missing',
            uploadDate: img?.uploadDate || 'missing',
            base64Length: img?.base64Data ? img.base64Data.length : 0,
            isValidObject: !!(img && typeof img === 'object' && img.filename && img.base64Data)
          };
          results.imageAnalysis.push(analysis);
        });

        results.parsingSteps.push({
          step: 5,
          operation: 'Analyze Images',
          status: 'success',
          message: `Analyzed ${kvImages.length} images`,
          validImages: results.imageAnalysis.filter(img => img.isValidObject).length,
          invalidImages: results.imageAnalysis.filter(img => !img.isValidObject).length
        });

      } else {
        results.parsingSteps.push({
          step: 5,
          operation: 'Analyze Images',
          status: 'error',
          message: `Final result is not an array: ${typeof kvImages}`
        });
      }

      results.parsedData = {
        type: typeof kvImages,
        isArray: Array.isArray(kvImages),
        length: Array.isArray(kvImages) ? kvImages.length : 'N/A',
        sample: Array.isArray(kvImages) ? kvImages.slice(0, 2).map(img => ({
          filename: img?.filename,
          title: img?.title,
          hasBase64: !!(img?.base64Data)
        })) : 'Not an array'
      };

    } catch (error) {
      console.error('Inner KV inspection error:', error);
      results.error = error.message;
      results.parsingSteps.push({
        step: 'error',
        operation: 'General Error',
        status: 'error',
        message: error.message,
        stack: error.stack
      });
    }

    console.log('=== DETAILED KV INSPECTION END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('KV inspection error:', error);
    return NextResponse.json({
      error: 'KV inspection failed',
      details: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
