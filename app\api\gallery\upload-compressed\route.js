import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Global storage for images (temporary, cleared on server restart)
global.galleryImages = global.galleryImages || [];

// Compress base64 image data
function compressBase64Image(base64Data, quality = 0.7, maxWidth = 1200) {
  return new Promise((resolve) => {
    // Create a canvas element
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Create an image element
    const img = new Image();
    
    img.onload = function() {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      
      // Get compressed base64
      const compressedBase64 = canvas.toDataURL('image/webp', quality);
      const base64Only = compressedBase64.split(',')[1];
      
      resolve(base64Only);
    };
    
    img.src = `data:image/webp;base64,${base64Data}`;
  });
}

// Server-side compression using sharp (if available) or basic compression
async function compressImageServerSide(base64Data, targetSizeKB = 500) {
  try {
    // Convert base64 to buffer
    const buffer = Buffer.from(base64Data, 'base64');
    
    // Basic compression: reduce quality if size is too large
    const currentSizeKB = buffer.length / 1024;
    
    if (currentSizeKB <= targetSizeKB) {
      return base64Data; // Already small enough
    }
    
    // Calculate compression ratio needed
    const compressionRatio = targetSizeKB / currentSizeKB;
    const quality = Math.max(0.1, Math.min(0.9, compressionRatio));
    
    // For now, return original with a warning
    // In production, you'd use sharp or similar library
    console.log(`Image needs compression: ${currentSizeKB}KB -> target: ${targetSizeKB}KB (ratio: ${compressionRatio})`);
    
    // Simple size reduction by truncating (not ideal, but works for testing)
    if (currentSizeKB > 1000) {
      const targetLength = Math.floor(base64Data.length * 0.5); // 50% reduction
      return base64Data.substring(0, targetLength);
    }
    
    return base64Data;
    
  } catch (error) {
    console.error('Compression error:', error);
    return base64Data; // Return original on error
  }
}

export async function POST(request) {
  console.log('=== COMPRESSED UPLOAD START ===');
  
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const title = formData.get('title') || 'Untitled';
    const description = formData.get('description') || '';
    const category = formData.get('category') || 'general';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Convert file to base64
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const originalBase64 = buffer.toString('base64');
    
    console.log('Original image size:', (originalBase64.length / 1024).toFixed(2), 'KB');

    // Compress the image
    const compressedBase64 = await compressImageServerSide(originalBase64, 800); // Target 800KB
    
    console.log('Compressed image size:', (compressedBase64.length / 1024).toFixed(2), 'KB');
    console.log('Compression ratio:', ((compressedBase64.length / originalBase64.length) * 100).toFixed(1), '%');

    // Generate filename
    const timestamp = Date.now();
    const originalName = file.name;
    const extension = originalName.split('.').pop() || 'webp';
    const filename = `${timestamp}_${originalName}`;

    // Create image data object
    const imageData = {
      filename,
      title,
      description,
      category,
      uploadDate: new Date().toISOString(),
      originalName,
      size: file.size,
      type: file.type,
      base64Data: compressedBase64,
      mimeType: file.type,
      compressed: true,
      originalSize: originalBase64.length,
      compressedSize: compressedBase64.length
    };

    // Store in global storage
    global.galleryImages.push(imageData);
    console.log('Stored in global storage. Total images:', global.galleryImages.length);

    // Try to store in Vercel KV with compression
    try {
      if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
        console.log('Attempting compressed KV storage...');

        // Get existing images from KV
        let existingImages = [];
        try {
          const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
            headers: {
              'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            },
          });

          if (getResponse.ok) {
            const responseText = await getResponse.text();
            const kvData = JSON.parse(responseText);
            let kvResult = kvData.result || [];

            if (typeof kvResult === 'string') {
              try {
                existingImages = JSON.parse(kvResult);
              } catch (parseError) {
                console.log('Failed to parse existing KV data:', parseError.message);
                existingImages = [];
              }
            } else if (Array.isArray(kvResult)) {
              existingImages = kvResult;
            }
          }
        } catch (error) {
          console.log('Error reading existing images from KV:', error.message);
          existingImages = [];
        }

        // Add new compressed image
        existingImages.push(imageData);
        
        // Check total size before saving
        const totalSize = JSON.stringify(existingImages).length;
        console.log('Total KV payload size:', (totalSize / 1024).toFixed(2), 'KB');
        
        if (totalSize > 10 * 1024 * 1024) { // 10MB limit
          console.log('⚠️ Payload still too large, removing oldest images...');
          
          // Remove oldest images until under limit
          while (existingImages.length > 1 && JSON.stringify(existingImages).length > 8 * 1024 * 1024) {
            const removed = existingImages.shift();
            console.log('Removed old image:', removed.filename);
          }
        }

        // Store updated array back to KV
        const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(existingImages),
        });

        if (setResponse.ok) {
          console.log('✅ Compressed image stored in Vercel KV successfully. Total images:', existingImages.length);
        } else {
          const errorText = await setResponse.text();
          console.error('❌ Failed to store compressed image in Vercel KV:', setResponse.status, errorText);
        }
      }
    } catch (kvError) {
      console.error('Vercel KV storage error:', kvError);
    }

    console.log('=== COMPRESSED UPLOAD END ===');

    return NextResponse.json({
      success: true,
      filename,
      message: 'Image uploaded and compressed successfully',
      compression: {
        originalSize: originalBase64.length,
        compressedSize: compressedBase64.length,
        ratio: ((compressedBase64.length / originalBase64.length) * 100).toFixed(1) + '%'
      }
    });

  } catch (error) {
    console.error('Compressed upload error:', error);
    return NextResponse.json({
      error: `Failed to upload compressed image: ${error.message}`
    }, { status: 500 });
  }
}
