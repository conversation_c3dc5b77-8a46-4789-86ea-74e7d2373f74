import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== DIRECT KV DELETE START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      operations: [],
      success: false
    };

    // Step 1: Read current KV data
    console.log('Step 1: Reading current KV data...');
    results.operations.push({ step: 1, operation: 'Read Current KV', status: 'start' });
    
    const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let currentImages = [];
    let beforeCount = 0;
    
    if (getResponse.ok) {
      const responseText = await getResponse.text();
      console.log('Raw KV response:', responseText.substring(0, 200));
      
      const kvData = JSON.parse(responseText);
      let kvResult = kvData.result || [];
      
      // Handle double-encoded JSON
      if (typeof kvResult === 'string') {
        try {
          currentImages = JSON.parse(kvResult);
          beforeCount = currentImages.length;
          console.log('Parsed images from KV string:', beforeCount);
        } catch (parseError) {
          console.log('Failed to parse KV string:', parseError.message);
          currentImages = [];
        }
      } else if (Array.isArray(kvResult)) {
        currentImages = kvResult;
        beforeCount = currentImages.length;
        console.log('Got images array from KV:', beforeCount);
      }
      
      results.operations.push({ 
        step: 1, 
        operation: 'Read Current KV', 
        status: 'success',
        beforeCount: beforeCount,
        rawDataLength: responseText.length
      });
      
      // Log current images
      results.beforeImages = currentImages.map(img => ({
        filename: img?.filename || 'unknown',
        title: img?.title || 'unknown',
        uploadDate: img?.uploadDate || 'unknown'
      }));
      
    } else {
      results.operations.push({ 
        step: 1, 
        operation: 'Read Current KV', 
        status: 'failed',
        httpStatus: getResponse.status
      });
      return NextResponse.json(results);
    }

    // Step 2: Filter out the specific test image
    console.log('Step 2: Filtering out test image...');
    results.operations.push({ step: 2, operation: 'Filter Test Image', status: 'start' });
    
    const filteredImages = currentImages.filter(img => {
      // Remove the specific test image
      const isTestImage = img.filename === 'test_1749770530809_kv_upload.webp' ||
                         img.title === 'KV Upload Test Image' ||
                         (img.filename && img.filename.includes('test_1749770530809'));
      
      if (isTestImage) {
        console.log('Found and removing test image:', img.filename);
        return false; // Remove this image
      }
      return true; // Keep other images
    });
    
    const removedCount = currentImages.length - filteredImages.length;
    console.log(`Filtered: ${currentImages.length} -> ${filteredImages.length} (removed ${removedCount})`);
    
    results.operations.push({ 
      step: 2, 
      operation: 'Filter Test Image', 
      status: 'success',
      beforeCount: currentImages.length,
      afterCount: filteredImages.length,
      removedCount: removedCount
    });

    // Step 3: Save filtered data back to KV
    console.log('Step 3: Saving filtered data to KV...');
    results.operations.push({ step: 3, operation: 'Save Filtered Data', status: 'start' });
    
    const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(filteredImages),
    });

    if (setResponse.ok) {
      console.log('Successfully saved filtered data to KV');
      results.operations.push({ 
        step: 3, 
        operation: 'Save Filtered Data', 
        status: 'success',
        savedCount: filteredImages.length
      });
    } else {
      const errorText = await setResponse.text();
      console.error('Failed to save filtered data:', setResponse.status, errorText);
      results.operations.push({ 
        step: 3, 
        operation: 'Save Filtered Data', 
        status: 'failed',
        httpStatus: setResponse.status,
        error: errorText
      });
      return NextResponse.json(results);
    }

    // Step 4: Wait and verify
    console.log('Step 4: Waiting 3 seconds then verifying...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    results.operations.push({ step: 4, operation: 'Verify Delete', status: 'start' });
    
    const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (verifyResponse.ok) {
      const verifyText = await verifyResponse.text();
      const verifyData = JSON.parse(verifyText);
      let verifyResult = verifyData.result || [];
      
      // Handle double-encoded JSON
      if (typeof verifyResult === 'string') {
        try {
          verifyResult = JSON.parse(verifyResult);
        } catch (e) {
          verifyResult = [];
        }
      }
      
      const finalCount = Array.isArray(verifyResult) ? verifyResult.length : 0;
      const testImageStillExists = Array.isArray(verifyResult) && 
        verifyResult.some(img => 
          img.filename === 'test_1749770530809_kv_upload.webp' ||
          img.title === 'KV Upload Test Image'
        );
      
      results.operations.push({ 
        step: 4, 
        operation: 'Verify Delete', 
        status: 'success',
        finalCount: finalCount,
        testImageStillExists: testImageStillExists
      });
      
      if (!testImageStillExists && finalCount === filteredImages.length) {
        results.success = true;
        results.message = 'Test image successfully deleted from KV!';
      } else if (testImageStillExists) {
        results.message = 'Test image still exists in KV after delete attempt';
      } else {
        results.message = `Unexpected final count: expected ${filteredImages.length}, got ${finalCount}`;
      }
      
      // Log final images
      results.afterImages = Array.isArray(verifyResult) ? verifyResult.map(img => ({
        filename: img?.filename || 'unknown',
        title: img?.title || 'unknown',
        uploadDate: img?.uploadDate || 'unknown'
      })) : [];
      
    } else {
      results.operations.push({ 
        step: 4, 
        operation: 'Verify Delete', 
        status: 'failed',
        httpStatus: verifyResponse.status
      });
    }

    // Step 5: Clear global storage
    console.log('Step 5: Clearing global storage...');
    try {
      global.galleryImages = [];
      results.operations.push({ 
        step: 5, 
        operation: 'Clear Global', 
        status: 'success'
      });
    } catch (globalError) {
      results.operations.push({ 
        step: 5, 
        operation: 'Clear Global', 
        status: 'failed',
        error: globalError.message
      });
    }

    console.log('=== DIRECT KV DELETE END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('Direct KV delete error:', error);
    return NextResponse.json({
      error: 'Direct KV delete failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
