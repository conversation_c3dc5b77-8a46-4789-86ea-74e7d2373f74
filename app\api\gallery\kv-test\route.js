import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET() {
  console.log('=== KV CONNECTION TEST ===');
  
  // Check for different possible KV variable names
  const kvVariants = {
    standard: {
      url: process.env.KV_REST_API_URL,
      token: process.env.KV_REST_API_TOKEN
    },
    alternative: {
      url: process.env.KV_URL,
      token: process.env.KV_REST_API_TOKEN
    },
    gallery: {
      url: process.env.GALLERY_KV_REST_API_URL,
      token: process.env.GALLERY_KV_REST_API_TOKEN
    }
  };

  const result = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    kvVariants: {
      standard: {
        urlConfigured: !!kvVariants.standard.url,
        tokenConfigured: !!kvVariants.standard.token,
        urlPreview: kvVariants.standard.url ?
          kvVariants.standard.url.substring(0, 30) + '...' : 'Not configured'
      },
      alternative: {
        urlConfigured: !!kvVariants.alternative.url,
        tokenConfigured: !!kvVariants.alternative.token,
        urlPreview: kvVariants.alternative.url ?
          kvVariants.alternative.url.substring(0, 30) + '...' : 'Not configured'
      },
      gallery: {
        urlConfigured: !!kvVariants.gallery.url,
        tokenConfigured: !!kvVariants.gallery.token,
        urlPreview: kvVariants.gallery.url ?
          kvVariants.gallery.url.substring(0, 30) + '...' : 'Not configured'
      }
    },
    test: {
      status: 'pending',
      message: '',
      error: null
    }
  };

  // Find which KV variant is configured
  let activeKV = null;
  for (const [name, config] of Object.entries(kvVariants)) {
    if (config.url && config.token) {
      activeKV = { name, ...config };
      break;
    }
  }

  // Test KV connection
  if (!activeKV) {
    result.test.status = 'failed';
    result.test.message = 'No KV environment variables configured';
    return NextResponse.json(result, { status: 400 });
  }

  result.test.activeVariant = activeKV.name;

  try {
    console.log('Testing KV connection with variant:', activeKV.name);

    // Try to read from KV
    const response = await fetch(`${activeKV.url}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${activeKV.token}`,
      },
    });

    console.log('KV response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      let images = data.result || [];
      
      // Handle double-encoded JSON
      if (typeof images === 'string') {
        try {
          images = JSON.parse(images);
        } catch (e) {
          images = [];
        }
      }
      
      result.test.status = 'success';
      result.test.message = `KV connection successful. Found ${Array.isArray(images) ? images.length : 'invalid'} images.`;
      result.test.imageCount = Array.isArray(images) ? images.length : 0;
      
    } else {
      const errorText = await response.text();
      result.test.status = 'failed';
      result.test.message = `KV request failed with status ${response.status}`;
      result.test.error = errorText;
    }
    
  } catch (error) {
    console.error('KV test error:', error);
    result.test.status = 'failed';
    result.test.message = 'KV connection error';
    result.test.error = error.message;
  }

  console.log('KV test result:', result);
  return NextResponse.json(result);
}
