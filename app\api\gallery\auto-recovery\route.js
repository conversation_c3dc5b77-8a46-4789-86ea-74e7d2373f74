import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Health check function
async function checkDataHealth() {
  const kvUrl = process.env.KV_REST_API_URL;
  const kvToken = process.env.KV_REST_API_TOKEN;
  
  if (!kvUrl || !kvToken) {
    return { healthy: false, error: 'KV not configured' };
  }
  
  try {
    const response = await fetch(`${kvUrl}/get/gallery_images`, {
      headers: { 'Authorization': `Bearer ${kvToken}` }
    });
    
    if (!response.ok) {
      return { healthy: false, error: 'Failed to fetch data', status: response.status };
    }
    
    const rawText = await response.text();
    const kvData = JSON.parse(rawText);
    let images = kvData.result;
    
    const issues = [];
    
    // Check for double-encoding
    if (typeof images === 'string') {
      issues.push('Double-encoded data detected');
      try {
        images = JSON.parse(images);
        issues.push('Double-encoding can be fixed');
      } catch (e) {
        issues.push('Double-encoding cannot be parsed');
        return { healthy: false, issues, canRecover: false };
      }
    }
    
    // Check if it's an array
    if (!Array.isArray(images)) {
      issues.push('Data is not an array');
      return { healthy: false, issues, canRecover: false };
    }
    
    // Check for data corruption in individual images
    const corruptedImages = [];
    images.forEach((img, index) => {
      if (!img || typeof img !== 'object') {
        corruptedImages.push(index);
      } else {
        const requiredFields = ['filename', 'title', 'category'];
        const missingFields = requiredFields.filter(field => !img[field]);
        if (missingFields.length > 0) {
          corruptedImages.push({ index, missingFields });
        }
      }
    });
    
    if (corruptedImages.length > 0) {
      issues.push(`${corruptedImages.length} corrupted images found`);
    }
    
    return {
      healthy: issues.length === 0,
      issues,
      imageCount: images.length,
      corruptedImages,
      canRecover: true,
      dataType: typeof kvData.result,
      isArray: Array.isArray(images)
    };
    
  } catch (error) {
    return { 
      healthy: false, 
      error: 'Health check failed', 
      details: error.message,
      canRecover: false 
    };
  }
}

// Auto-recovery function
async function performAutoRecovery() {
  const kvUrl = process.env.KV_REST_API_URL;
  const kvToken = process.env.KV_REST_API_TOKEN;
  
  const recoverySteps = [];
  
  try {
    // Step 1: Get current data
    const response = await fetch(`${kvUrl}/get/gallery_images`, {
      headers: { 'Authorization': `Bearer ${kvToken}` }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch data: ${response.status}`);
    }
    
    const rawText = await response.text();
    const kvData = JSON.parse(rawText);
    let images = kvData.result;
    
    recoverySteps.push('Fetched current data');
    
    // Step 2: Fix double-encoding
    if (typeof images === 'string') {
      recoverySteps.push('Detected double-encoding');
      try {
        images = JSON.parse(images);
        recoverySteps.push('Fixed double-encoding');
      } catch (e) {
        // Try to recover from backup
        recoverySteps.push('Double-encoding unfixable, attempting backup recovery');
        
        const backupResponse = await fetch(`${kvUrl}/get/gallery_backup_latest`, {
          headers: { 'Authorization': `Bearer ${kvToken}` }
        });
        
        if (backupResponse.ok) {
          const backupText = await backupResponse.text();
          const backupData = JSON.parse(backupText);
          const backup = JSON.parse(backupData.result);
          
          if (backup.images && Array.isArray(backup.images)) {
            images = backup.images;
            recoverySteps.push('Recovered from latest backup');
          } else {
            images = [];
            recoverySteps.push('Backup invalid, starting with empty array');
          }
        } else {
          images = [];
          recoverySteps.push('No backup available, starting with empty array');
        }
      }
    }
    
    // Step 3: Ensure it's an array
    if (!Array.isArray(images)) {
      recoverySteps.push('Data not an array, converting to array');
      images = [];
    }
    
    // Step 4: Fix corrupted images
    const fixedImages = [];
    images.forEach((img, index) => {
      if (!img || typeof img !== 'object') {
        recoverySteps.push(`Removed corrupted image at index ${index}`);
        return;
      }
      
      const fixedImg = { ...img };
      let wasFixed = false;
      
      if (!fixedImg.filename) {
        fixedImg.filename = `recovered_${index}_${Date.now()}`;
        wasFixed = true;
      }
      if (!fixedImg.title) {
        fixedImg.title = 'Recovered Image';
        wasFixed = true;
      }
      if (!fixedImg.category) {
        fixedImg.category = 'general';
        wasFixed = true;
      }
      if (!fixedImg.uploadDate) {
        fixedImg.uploadDate = new Date().toISOString();
        wasFixed = true;
      }
      
      if (wasFixed) {
        recoverySteps.push(`Fixed missing fields for image ${index}`);
      }
      
      fixedImages.push(fixedImg);
    });
    
    // Step 5: Save the recovered data
    const saveResponse = await fetch(`${kvUrl}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${kvToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(fixedImages), // Single encoding only
    });
    
    if (!saveResponse.ok) {
      throw new Error(`Failed to save recovered data: ${saveResponse.status}`);
    }
    
    recoverySteps.push('Saved recovered data');
    
    // Step 6: Create recovery backup
    await fetch(`${kvUrl}/set/gallery_backup_recovery`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${kvToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        timestamp: new Date().toISOString(),
        operation: 'auto-recovery',
        imageCount: fixedImages.length,
        recoverySteps,
        images: fixedImages
      }),
    });
    
    recoverySteps.push('Created recovery backup');
    
    return {
      success: true,
      recoveredImages: fixedImages.length,
      recoverySteps
    };
    
  } catch (error) {
    recoverySteps.push(`Recovery failed: ${error.message}`);
    return {
      success: false,
      error: error.message,
      recoverySteps
    };
  }
}

// POST endpoint for manual recovery
export async function POST() {
  console.log('=== AUTO RECOVERY SYSTEM ===');
  
  try {
    const healthCheck = await checkDataHealth();
    
    if (healthCheck.healthy) {
      return NextResponse.json({
        success: true,
        message: 'Data is healthy, no recovery needed',
        healthCheck
      });
    }
    
    if (!healthCheck.canRecover) {
      return NextResponse.json({
        success: false,
        message: 'Data corruption detected but cannot be recovered',
        healthCheck
      }, { status: 500 });
    }
    
    console.log('Data corruption detected, performing auto-recovery...');
    const recovery = await performAutoRecovery();
    
    return NextResponse.json({
      success: recovery.success,
      message: recovery.success ? 'Auto-recovery completed successfully' : 'Auto-recovery failed',
      healthCheck,
      recovery,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Auto-recovery system failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Auto-recovery system failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET endpoint for health monitoring
export async function GET() {
  try {
    const healthCheck = await checkDataHealth();
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      ...healthCheck,
      recommendations: healthCheck.healthy ? 
        ['Data is healthy'] : 
        [
          'Run auto-recovery to fix issues',
          'Create backup before making changes',
          'Use protected upload for new images'
        ]
    });
    
  } catch (error) {
    return NextResponse.json({
      healthy: false,
      error: 'Monitoring failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
