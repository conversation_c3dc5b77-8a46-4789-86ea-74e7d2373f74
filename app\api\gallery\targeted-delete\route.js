import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== TARGETED IMAGE DELETION START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    // Define the exact 4 persistent images to target
    const targetImages = [
      {
        title: "Bathroom Shower Renovation, 2025",
        description: "Entire Bathroom Shower gut; Walls, Base, and Door. We installed this beautiful tile along with a brand new shower base and very popular glass for the exterior."
      },
      {
        title: "Basement",
        description: " Basement Renovations."
      },
      {
        title: "test",
        description: "test"
      }
      // Note: Only 3 entries because images 3 and 4 have the same title/description
    ];

    const results = {
      timestamp: new Date().toISOString(),
      operations: [],
      success: false,
      imagesFound: [],
      imagesDeleted: [],
      finalCount: 0
    };

    // Step 1: Get current KV data
    try {
      console.log('🔍 Step 1: Getting current KV data...');
      
      const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
        headers: {
          'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        },
      });

      if (getResponse.ok) {
        const responseText = await getResponse.text();
        const kvData = JSON.parse(responseText);
        let existingImages = kvData.result || [];
        
        // Handle double-encoded JSON
        if (typeof existingImages === 'string') {
          try {
            existingImages = JSON.parse(existingImages);
          } catch (e) {
            existingImages = [];
          }
        }
        
        if (!Array.isArray(existingImages)) {
          existingImages = [];
        }
        
        console.log(`Found ${existingImages.length} total images in KV`);
        
        results.operations.push({
          step: 1,
          operation: 'Get KV Data',
          status: 'success',
          message: `Found ${existingImages.length} images in KV`
        });

        // Step 2: Identify target images
        console.log('🎯 Step 2: Identifying target images...');
        
        const foundTargets = [];
        
        existingImages.forEach((img, index) => {
          if (!img || !img.title) return;
          
          // Check if this image matches any of our targets - UPDATED WITH EXACT FILENAME
          const isTarget = targetImages.some(target =>
            (img.title === target.title && img.description === target.description) ||
            (img.title === "test" && img.description === "test") ||
            img.filename?.includes('1749511630674_flooring_2.png') ||
            img.filename?.includes('1749511944989_flooring_2.png') ||
            // NEW: Target the exact persistent bathroom image
            img.filename?.includes('1749608429634_1749557178374_IMG_5972.webp') ||
            (img.title === "Bathroom Shower Renovation, 2025") ||
            img.filename?.includes('1749608429634_1749557178374') ||
            // NEWER: Target the KV test image that's now persistent
            img.filename?.includes('test_1749770530809_kv_upload.webp') ||
            (img.title === "KV Upload Test Image") ||
            img.filename?.includes('test_') && img.filename?.includes('kv_upload')
          );
          
          if (isTarget) {
            foundTargets.push({
              index,
              filename: img.filename,
              title: img.title,
              description: img.description,
              uploadDate: img.uploadDate
            });
          }
        });
        
        results.imagesFound = foundTargets;
        console.log(`🎯 Found ${foundTargets.length} target images to delete`);
        
        results.operations.push({
          step: 2,
          operation: 'Identify Targets',
          status: 'success',
          message: `Identified ${foundTargets.length} target images`,
          targets: foundTargets
        });

        // Step 3: Remove target images
        console.log('🗑️ Step 3: Removing target images...');
        
        const cleanedImages = existingImages.filter(img => {
          if (!img || !img.title) return true; // Keep images without titles
          
          // Remove if it matches any target - UPDATED WITH EXACT FILENAME
          const shouldRemove = targetImages.some(target =>
            (img.title === target.title && img.description === target.description) ||
            (img.title === "test" && img.description === "test") ||
            img.filename?.includes('1749511630674_flooring_2.png') ||
            img.filename?.includes('1749511944989_flooring_2.png') ||
            // NEW: Target the exact persistent bathroom image
            img.filename?.includes('1749608429634_1749557178374_IMG_5972.webp') ||
            (img.title === "Bathroom Shower Renovation, 2025") ||
            img.filename?.includes('1749608429634_1749557178374') ||
            // NEWER: Target the KV test image that's now persistent
            img.filename?.includes('test_1749770530809_kv_upload.webp') ||
            (img.title === "KV Upload Test Image") ||
            img.filename?.includes('test_') && img.filename?.includes('kv_upload')
          );
          
          if (shouldRemove) {
            results.imagesDeleted.push({
              filename: img.filename,
              title: img.title,
              description: img.description
            });
            console.log(`🗑️ Removing: ${img.title} (${img.filename})`);
          }
          
          return !shouldRemove; // Keep if NOT a target
        });
        
        console.log(`Cleaned array: ${existingImages.length} → ${cleanedImages.length} images`);
        
        results.operations.push({
          step: 3,
          operation: 'Remove Target Images',
          status: 'success',
          message: `Removed ${existingImages.length - cleanedImages.length} target images`,
          beforeCount: existingImages.length,
          afterCount: cleanedImages.length
        });

        // Step 4: Save cleaned data back to KV
        console.log('💾 Step 4: Saving cleaned data to KV...');
        
        const setResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(cleanedImages),
        });

        if (setResponse.ok) {
          results.finalCount = cleanedImages.length;
          results.success = true;
          
          results.operations.push({
            step: 4,
            operation: 'Save Cleaned Data',
            status: 'success',
            message: `Saved ${cleanedImages.length} clean images to KV`
          });
          
          console.log(`✅ Successfully saved ${cleanedImages.length} clean images to KV`);
        } else {
          const errorText = await setResponse.text();
          results.operations.push({
            step: 4,
            operation: 'Save Cleaned Data',
            status: 'error',
            message: `Failed to save: ${setResponse.status} - ${errorText}`
          });
        }

      } else {
        results.operations.push({
          step: 1,
          operation: 'Get KV Data',
          status: 'error',
          message: `Failed to get KV data: ${getResponse.status}`
        });
      }

    } catch (kvError) {
      results.operations.push({
        step: 1,
        operation: 'Get KV Data',
        status: 'error',
        message: kvError.message
      });
    }

    // Step 5: Clear global storage too
    global.galleryImages = [];
    results.operations.push({
      step: 5,
      operation: 'Clear Global Storage',
      status: 'success',
      message: 'Global storage cleared'
    });

    console.log('=== TARGETED IMAGE DELETION END ===');
    console.log(`Success: ${results.success}, Deleted: ${results.imagesDeleted.length}, Final count: ${results.finalCount}`);
    
    return NextResponse.json(results);

  } catch (error) {
    console.error('Targeted delete error:', error);
    return NextResponse.json({
      error: 'Targeted delete failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
