# Cloudinary Upload Preset Setup

## Why You Need This
Your gallery uses an "unsigned upload preset" which allows secure uploads without exposing your API secret in the browser.

## Quick Setup (2 minutes)

### Step 1: Go to Cloudinary Settings
1. Log into [cloudinary.com](https://cloudinary.com)
2. Go to **Settings** → **Upload**
3. Scroll down to **"Upload presets"**

### Step 2: Create Upload Preset
1. Click **"Add upload preset"**
2. Fill in these settings:

**Basic Settings:**
- **Preset name**: `gallery_preset`
- **Signing Mode**: `Unsigned` ⚠️ (This is important!)
- **Use filename**: ✅ Yes
- **Unique filename**: ✅ Yes
- **Overwrite**: ❌ No

**Upload Options:**
- **Folder**: `express_renos_gallery`
- **Resource type**: `Image`
- **Access mode**: `Public`

**Advanced Options (Optional):**
- **Auto tagging**: ✅ Enable
- **Quality**: `Auto`
- **Format**: `Auto`

3. Click **"Save"**

### Step 3: Test the Setup
After creating the preset, test your gallery:

1. Go to your admin panel: [expressrenos.com/admin/gallery](https://expressrenos.com/admin/gallery)
2. Upload a test image
3. Check that it appears in your gallery
4. Try deleting the image to test delete functionality

### Troubleshooting

**If upload fails with "Invalid upload preset":**
- Make sure the preset name is exactly `gallery_preset`
- Make sure "Signing Mode" is set to "Unsigned"
- Wait 1-2 minutes after creating the preset

**If images upload but don't appear:**
- Check your Cloudinary Media Library
- Verify the folder is set to `express_renos_gallery`

**If delete doesn't work:**
- The image will be removed from your gallery immediately
- Cloudinary deletion might take a few seconds
- Check the browser console for any error messages

### Your Gallery is Now:
- ✅ **Data Loss Proof**: Images stored permanently in Cloudinary
- ✅ **Fast Loading**: Global CDN delivery
- ✅ **Auto-Optimized**: Images automatically optimized for web
- ✅ **Scalable**: Handles unlimited images
- ✅ **Backed Up**: Automatic redundancy and backups

## Next Steps
1. Create the upload preset (above)
2. Test upload/delete functionality
3. Your gallery is ready for production use!
