import { NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function POST(request) {
  console.log('=== FORCE CLEAR KV START ===');
  
  try {
    // Get the password from request body
    const body = await request.json();
    const { password } = body;
    
    // Simple password check
    if (password !== 'ExpressRenos2024!') {
      return NextResponse.json({ 
        error: 'Unauthorized',
        message: 'Invalid password'
      }, { status: 401 });
    }

    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      return NextResponse.json({ 
        error: 'KV not configured',
        message: 'Vercel KV environment variables not found'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      operations: [],
      success: false
    };

    // Step 1: Read what's currently in KV
    console.log('Step 1: Reading current KV data...');
    results.operations.push({ step: 1, operation: 'Read Current KV', status: 'start' });
    
    const getResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    let beforeData = null;
    if (getResponse.ok) {
      const responseText = await getResponse.text();
      beforeData = responseText;
      console.log('Current KV raw data:', responseText.substring(0, 200) + '...');
      
      results.operations.push({ 
        step: 1, 
        operation: 'Read Current KV', 
        status: 'success',
        rawDataLength: responseText.length,
        preview: responseText.substring(0, 100)
      });
    } else {
      results.operations.push({ 
        step: 1, 
        operation: 'Read Current KV', 
        status: 'failed',
        httpStatus: getResponse.status
      });
    }

    // Step 2: Force set KV to empty array
    console.log('Step 2: Force setting KV to empty array...');
    results.operations.push({ step: 2, operation: 'Force Clear KV', status: 'start' });
    
    const clearResponse = await fetch(`${process.env.KV_REST_API_URL}/set/gallery_images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify([]), // Force empty array
    });

    if (clearResponse.ok) {
      console.log('KV force clear successful');
      results.operations.push({ 
        step: 2, 
        operation: 'Force Clear KV', 
        status: 'success',
        message: 'KV set to empty array'
      });
    } else {
      const errorText = await clearResponse.text();
      console.error('KV force clear failed:', clearResponse.status, errorText);
      results.operations.push({ 
        step: 2, 
        operation: 'Force Clear KV', 
        status: 'failed',
        httpStatus: clearResponse.status,
        error: errorText
      });
      return NextResponse.json(results);
    }

    // Step 3: Wait a moment then verify
    console.log('Step 3: Waiting 2 seconds then verifying...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    results.operations.push({ step: 3, operation: 'Verify Clear', status: 'start' });
    
    const verifyResponse = await fetch(`${process.env.KV_REST_API_URL}/get/gallery_images`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
    });

    if (verifyResponse.ok) {
      const verifyText = await verifyResponse.text();
      console.log('Verification result:', verifyText);
      
      const verifyData = JSON.parse(verifyText);
      let verifyResult = verifyData.result || [];
      
      // Handle double-encoded JSON
      if (typeof verifyResult === 'string') {
        try {
          verifyResult = JSON.parse(verifyResult);
        } catch (e) {
          verifyResult = [];
        }
      }
      
      const finalCount = Array.isArray(verifyResult) ? verifyResult.length : 0;
      
      results.operations.push({ 
        step: 3, 
        operation: 'Verify Clear', 
        status: 'success',
        finalCount: finalCount,
        rawResponse: verifyText.substring(0, 100)
      });
      
      if (finalCount === 0) {
        results.success = true;
        results.message = 'KV successfully cleared and verified empty!';
      } else {
        results.message = `Clear verification failed - ${finalCount} images still found`;
        results.remainingImages = Array.isArray(verifyResult) ? verifyResult.map(img => ({
          filename: img?.filename,
          title: img?.title
        })) : [];
      }
      
    } else {
      results.operations.push({ 
        step: 3, 
        operation: 'Verify Clear', 
        status: 'failed',
        httpStatus: verifyResponse.status
      });
    }

    // Step 4: Clear global storage
    console.log('Step 4: Clearing global storage...');
    results.operations.push({ step: 4, operation: 'Clear Global', status: 'start' });
    
    try {
      global.galleryImages = [];
      results.operations.push({ 
        step: 4, 
        operation: 'Clear Global', 
        status: 'success'
      });
    } catch (globalError) {
      results.operations.push({ 
        step: 4, 
        operation: 'Clear Global', 
        status: 'failed',
        error: globalError.message
      });
    }

    console.log('=== FORCE CLEAR KV END ===');
    return NextResponse.json(results);

  } catch (error) {
    console.error('Force clear KV error:', error);
    return NextResponse.json({
      error: 'Force clear failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
