'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

export default function Gallery() {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedImage, setSelectedImage] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showModalFooter, setShowModalFooter] = useState(true);

  const categories = [
    { value: 'all', label: 'All Projects' },
    { value: 'demolition', label: 'Demolition' },
    { value: 'renovation', label: 'Renovation' },
    { value: 'flooring', label: 'Flooring' },
    { value: 'painting', label: 'Painting' },
    { value: 'tiling', label: 'Tiling' },
    { value: 'landscaping', label: 'Landscaping' },
    { value: 'before-after', label: 'Before & After' },
    { value: 'general', label: 'General' }
  ];

  useEffect(() => {
    fetchImages();
  }, []);

  const fetchImages = async () => {
    try {
      const response = await fetch('/api/gallery/simple-cloudinary');
      const data = await response.json();
      setImages(data.images || []);
    } catch (error) {
      console.error('Error fetching images:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredImages = selectedCategory === 'all'
    ? images
    : images.filter(img => img.category === selectedCategory);

  const openModal = (image) => {
    setSelectedImage(image);
    setIsModalOpen(true);
    setShowModalFooter(true); // Reset footer visibility
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';

    // Hide footer after 2 seconds
    setTimeout(() => {
      setShowModalFooter(false);
    }, 2000);
  };

  const closeModal = () => {
    setSelectedImage(null);
    setIsModalOpen(false);
    setShowModalFooter(true); // Reset footer state for next time
    // Restore body scroll
    document.body.style.overflow = 'unset';
  };

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isModalOpen) {
        closeModal();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isModalOpen]);

  // Cleanup: restore body scroll on component unmount
  useEffect(() => {
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  if (loading) {
    return (
      <div className="pt-20 pb-10 px-4 bg-gray-900 min-h-screen">
        <div className="max-w-7xl mx-auto text-center text-white">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-700 rounded w-1/3 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-700 rounded w-1/4 mx-auto mb-8"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-gray-800 rounded-lg h-64"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 pb-10 px-4 bg-gray-900 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-extrabold text-center mb-8 text-transparent bg-clip-text bg-gradient-to-r from-amber-600 via-yellow-200 to-yellow-400">
          Project Gallery
        </h1>
        <hr className="border-yellow-300 mb-8 w-1/4 mx-auto" />

        {/* Category Filter */}
        <div className="mb-8 text-center">
          <div className="inline-flex flex-wrap gap-2 bg-gray-800 p-2 rounded-lg">
            {categories.map((category) => (
              <button
                key={category.value}
                onClick={() => setSelectedCategory(category.value)}
                className={`px-4 py-2 rounded-md transition-colors duration-200 ${
                  selectedCategory === category.value
                    ? 'bg-yellow-400 text-black font-bold'
                    : 'bg-gray-700 text-white hover:bg-gray-600'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Images Grid */}
        {filteredImages.length === 0 ? (
          <div className="text-center text-white">
            <p className="text-xl mb-4">
              {selectedCategory === 'all'
                ? 'No images found in the gallery.'
                : `No images found in the "${categories.find(c => c.value === selectedCategory)?.label}" category.`
              }
            </p>
            <p className="text-gray-400">
              {selectedCategory === 'all'
                ? 'Images will appear here once uploaded through the admin panel.'
                : 'Try selecting a different category or upload images to this category.'
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredImages.map((image) => (
              <div
                key={image.filename}
                className="overflow-hidden rounded-lg shadow-xl bg-gray-800 hover:shadow-2xl transition-all duration-300 group cursor-pointer"
                onClick={() => openModal(image)}
              >
                <div className="relative">
                  <Image
                    src={image.url}
                    alt={image.title}
                    width={300}
                    height={200}
                    className="object-cover w-full h-48 group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                    <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 p-4">
                      <h3 className="font-bold text-lg mb-2">{image.title}</h3>
                      {image.description && (
                        <p className="text-sm">{image.description}</p>
                      )}
                      <div className="mt-2 text-xs opacity-75">Click to view full size</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Modal for full-size image */}
        {isModalOpen && selectedImage && (
          <div
            className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4"
            onClick={closeModal}
          >
            <div className="relative max-w-full max-h-full">
              {/* Close button */}
              <button
                onClick={closeModal}
                className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 hover:bg-opacity-75 text-white rounded-full p-2 transition-all duration-200"
                aria-label="Close modal"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>

              {/* Image */}
              <div
                className="relative"
                onClick={(e) => e.stopPropagation()} // Prevent closing when clicking on image
                onMouseEnter={() => setShowModalFooter(true)} // Show footer on hover
                onMouseLeave={() => {
                  // Hide footer again after a short delay when mouse leaves
                  setTimeout(() => setShowModalFooter(false), 2000);
                }}
              >
                <Image
                  src={selectedImage.url}
                  alt={selectedImage.title}
                  width={selectedImage.width || 1200}
                  height={selectedImage.height || 800}
                  className="max-w-full max-h-[90vh] object-contain rounded-lg"
                  priority
                />

                {/* Image info overlay with fade effect */}
                <div
                  className={`absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-4 rounded-b-lg transition-opacity duration-1000 ${
                    showModalFooter ? 'opacity-100' : 'opacity-0'
                  }`}
                >
                  <h3 className="text-xl font-bold mb-1">{selectedImage.title}</h3>
                  {selectedImage.description && (
                    <p className="text-sm text-gray-300">{selectedImage.description}</p>
                  )}
                  <p className="text-xs text-gray-400 mt-2">
                    Category: {selectedImage.category} •
                    Uploaded: {new Date(selectedImage.uploadDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

      </div>
    </div>
  );
}


